'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers/index'
import { getSubscriptionStatus } from '@/lib/utils'
import { downloadCSV, downloadExcel, convertToCSV, ExportMember } from '@/lib/export'
import { createTestMembers, clearTestData } from '@/lib/test-data'
import { MemberCreditStorage } from '@/lib/member-credit-storage'
// Remove database imports - using localStorage instead
import { useToast } from '@/hooks/use-toast'
import { AddMemberModal } from '@/components/members/add-member-modal'
import { EditMemberModal } from '@/components/members/edit-member-modal'
import { ViewMemberModal } from '@/components/members/view-member-modal'
import { MembersTable } from '@/components/members/members-table'
import { CategoriesModal } from '@/components/categories/categories-modal'
import {
  Users,
  Search,
  UserPlus,
  AlertTriangle,
  CheckCircle,
  Clock,
  Trash2,
  Download,
  FileText,
  FileSpreadsheet,
  CheckSquare,
  Square,
  Plus,
  Tags,
  CreditCard,
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email: string | null
  pregnant: boolean
  situation: string
  remarks: string | null
  created_at: string
  subscriptions: Subscription[]
}

interface Subscription {
  id: string
  sport: string
  plan_type: 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  price_dzd: number
  status: 'active' | 'expiring' | 'expired'
}



export default function MembersPage() {
  const [members, setMembers] = useState<Member[]>([])
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'expiring' | 'expired'>('all')
  const [creditFilter, setCreditFilter] = useState<'all' | 'has_credit'>('all')
  const [loading, setLoading] = useState(true)
  const [selectedMembers, setSelectedMembers] = useState<Set<string>>(new Set())
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showViewModal, setShowViewModal] = useState(false)
  const [showCategoriesModal, setShowCategoriesModal] = useState(false)
  const [editingMember, setEditingMember] = useState<Member | null>(null)
  const [viewingMember, setViewingMember] = useState<Member | null>(null)
  const [bulkLoading, setBulkLoading] = useState(false)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    fetchMembers()
  }, [])

  useEffect(() => {
    filterMembers()
  }, [members, searchQuery, statusFilter, creditFilter])

  const fetchMembers = () => {
    try {
      setLoading(true)

      // Fetch users from localStorage
      const storedUsers = localStorage.getItem('gym_members')
      const users = storedUsers ? JSON.parse(storedUsers) : []

      // Fetch subscriptions from localStorage
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []

      // Map users with their subscriptions
      const membersWithSubscriptions = users.map((user: any) => ({
        ...user,
        subscriptions: subscriptions
          .filter((sub: any) => sub.user_id === user.id)
          .map((sub: any) => ({
            id: sub.id,
            sport: sub.sport,
            plan_type: sub.plan_type as 'monthly' | 'quarterly' | 'yearly',
            start_date: sub.start_date,
            end_date: sub.end_date,
            price_dzd: sub.price_dzd,
            status: sub.status as 'active' | 'expiring' | 'expired'
          }))
      }))

      // Sync credit data with members
      MemberCreditStorage.syncMemberCreditData()

      setMembers(membersWithSubscriptions)
    } catch (error) {
      console.error('Error loading members:', error)
      toast({
        title: 'Error',
        description: 'Failed to load members from localStorage',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const filterMembers = () => {
    // Add safety checks for member data
    const validMembers = members.filter(member =>
      member &&
      typeof member === 'object' &&
      member.full_name &&
      member.phone
    )

    let filtered = validMembers.filter(member => {
      const nameMatch = member.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) || false
      const phoneMatch = member.phone?.includes(searchQuery) || false
      const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false

      return nameMatch || phoneMatch || emailMatch
    })

    if (statusFilter !== 'all') {
      filtered = filtered.filter(member => {
        const subscriptions = member.subscriptions || []
        if (subscriptions.length === 0) {
          return statusFilter === 'expired'
        }

        const activeSubscriptions = subscriptions.filter(sub =>
          sub && sub.end_date && getSubscriptionStatus(sub.end_date) === statusFilter
        )
        return activeSubscriptions && activeSubscriptions.length > 0
      })
    }

    // Credit filtering
    if (creditFilter !== 'all') {
      filtered = filtered.filter(member => {
        const creditLimit = member.credit_limit || 0

        switch (creditFilter) {
          case 'has_credit':
            return creditLimit > 0
          default:
            return true
        }
      })
    }

    setFilteredMembers(filtered)
  }

  const getActiveSubscription = (member: Member) => {
    if (!member) return null

    const subscriptions = member.subscriptions || []
    if (subscriptions.length === 0) return null

    // Get the most recent subscription (sorted by end date)
    const sortedSubscriptions = subscriptions
      .filter(sub => sub && sub.end_date)
      .sort((a, b) => new Date(b.end_date).getTime() - new Date(a.end_date).getTime())

    return sortedSubscriptions[0] || null
  }

  const getMemberStatus = (member: Member) => {
    if (!member) return 'expired'

    const subscriptions = member.subscriptions || []
    if (subscriptions.length === 0) return 'expired'

    // Get the most recent subscription (active or most recent)
    const sortedSubscriptions = subscriptions.sort((a, b) =>
      new Date(b.end_date).getTime() - new Date(a.end_date).getTime()
    )

    const latestSubscription = sortedSubscriptions[0]
    if (!latestSubscription || !latestSubscription.end_date) return 'expired'

    return getSubscriptionStatus(latestSubscription.end_date)
  }

  const deleteMember = (memberId: string) => {
    if (!confirm('Are you sure you want to delete this member?')) return

    try {
      // Delete from localStorage
      const storedUsers = localStorage.getItem('gym_members')
      const users = storedUsers ? JSON.parse(storedUsers) : []
      const updatedUsers = users.filter((user: any) => user.id !== memberId)
      localStorage.setItem('gym_members', JSON.stringify(updatedUsers))

      // Also delete related subscriptions
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      const updatedSubscriptions = subscriptions.filter((sub: any) => sub.user_id !== memberId)
      localStorage.setItem('gym_subscriptions', JSON.stringify(updatedSubscriptions))

      toast({
        title: t('member_deleted'),
        description: 'Member has been successfully deleted',
      })

      fetchMembers()
    } catch (error) {
      console.error('Error deleting member:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete member',
        variant: 'destructive',
      })
    }
  }

  // Export functions
  const handleExportCSV = () => {
    const exportData: ExportMember[] = filteredMembers.map(member => ({
      ...member,
      email: member.email || undefined,
      pregnant: member.pregnant || undefined,
      remarks: member.remarks || undefined,
      subscriptions: member.subscriptions || []
    }))
    const csvData = convertToCSV(exportData)
    downloadCSV(csvData, `members-${new Date().toISOString().split('T')[0]}.csv`)
    toast({
      title: 'Export Complete',
      description: 'Members data exported to CSV successfully',
    })
  }

  const handleExportExcel = async () => {
    const exportData: ExportMember[] = filteredMembers.map(member => ({
      ...member,
      email: member.email || undefined,
      pregnant: member.pregnant || undefined,
      remarks: member.remarks || undefined,
      subscriptions: member.subscriptions || []
    }))
    await downloadExcel(exportData, `members-${new Date().toISOString().split('T')[0]}.xlsx`)
    toast({
      title: 'Export Complete',
      description: 'Members data exported to Excel successfully',
    })
  }

  // Bulk operations
  const toggleSelectAll = () => {
    if (selectedMembers.size === filteredMembers.length) {
      setSelectedMembers(new Set())
    } else {
      setSelectedMembers(new Set(filteredMembers.map(m => m.id)))
    }
  }

  const handleBulkDelete = () => {
    if (selectedMembers.size === 0) return
    if (!confirm(`Are you sure you want to delete ${selectedMembers.size} members?`)) return

    setBulkLoading(true)
    try {
      // Delete from localStorage
      const storedUsers = localStorage.getItem('gym_members')
      const users = storedUsers ? JSON.parse(storedUsers) : []
      const updatedUsers = users.filter((user: any) => !selectedMembers.has(user.id))
      localStorage.setItem('gym_members', JSON.stringify(updatedUsers))

      // Also delete related subscriptions
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      const updatedSubscriptions = subscriptions.filter((sub: any) => !selectedMembers.has(sub.user_id))
      localStorage.setItem('gym_subscriptions', JSON.stringify(updatedSubscriptions))

      toast({
        title: 'Bulk Delete Complete',
        description: `${selectedMembers.size} members deleted successfully`,
      })

      setSelectedMembers(new Set())
      fetchMembers()
    } catch (error) {
      console.error('Error deleting members:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete selected members',
        variant: 'destructive',
      })
    } finally {
      setBulkLoading(false)
    }
  }

  const handleEditMember = (member: Member) => {
    setEditingMember(member)
    setShowEditModal(true)
  }

  const handleCreateTestData = () => {
    createTestMembers()
    toast({
      title: 'Test Data Created',
      description: 'Test members with different subscription statuses have been created',
    })
    fetchMembers()
  }

  const handleClearTestData = () => {
    if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
      clearTestData()
      toast({
        title: 'Data Cleared',
        description: 'All test data has been removed',
      })
      fetchMembers()
    }
  }

  const handleViewDetails = (member: Member) => {
    setViewingMember(member)
    setShowViewModal(true)
  }

  const handlePrintReport = (member: Member) => {
    // Create a simple print report
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      const subscriptions = member.subscriptions || []


      printWindow.document.write(`
        <html>
          <head>
            <title>Member Report - ${member.full_name}</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              .header { text-align: center; margin-bottom: 30px; }
              .info { margin-bottom: 20px; }
              .label { font-weight: bold; }
              table { width: 100%; border-collapse: collapse; margin-top: 20px; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>ÉLITE CLUB</h1>
              <h2>Member Report</h2>
            </div>
            <div class="info">
              <p><span class="label">Name:</span> ${member.full_name}</p>
              <p><span class="label">Phone:</span> ${member.phone}</p>
              <p><span class="label">Email:</span> ${member.email || 'N/A'}</p>
              <p><span class="label">Age:</span> ${member.age}</p>
              <p><span class="label">Gender:</span> ${member.gender}</p>
              <p><span class="label">Status:</span> ${member.situation}</p>
              <p><span class="label">Member Since:</span> ${new Date(member.created_at).toLocaleDateString()}</p>
            </div>
            <h3>Subscriptions</h3>
            <table>
              <tr>
                <th>Sport</th>
                <th>Plan</th>
                <th>Start Date</th>
                <th>End Date</th>
                <th>Price</th>
                <th>Status</th>
              </tr>
              ${subscriptions.map(sub => `
                <tr>
                  <td>${sub.sport}</td>
                  <td>${sub.plan_type}</td>
                  <td>${sub.start_date}</td>
                  <td>${sub.end_date}</td>
                  <td>${sub.price_dzd} DZD</td>
                  <td>${sub.status}</td>
                </tr>
              `).join('')}
            </table>
            <div style="margin-top: 40px; text-align: center; font-size: 12px;">
              <p>Generated on ${new Date().toLocaleString()}</p>
              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>
            </div>
          </body>
        </html>
      `)
      printWindow.document.close()
      printWindow.print()
    }
  }

  if (loading) {
    return (
      <MainLayout title={t('members')}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout title={t('members')}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-black text-gray-900 dark:text-white tracking-tight">
              {t('members_management')}
            </h1>
            <p className="text-base font-semibold text-gray-600 dark:text-gray-400">
              {t('manage_gym_members')}
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* Export Buttons */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleExportCSV}>
                  <FileText className="w-4 h-4 mr-2" />
                  {t('export_csv')}
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExportExcel}>
                  <FileSpreadsheet className="w-4 h-4 mr-2" />
                  {t('export_excel')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Bulk Operations */}
            {selectedMembers.size > 0 && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <CheckSquare className="w-4 h-4 mr-2" />
                    {t('bulk_operations')} ({selectedMembers.size})
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={handleBulkDelete}
                    className="text-red-600 dark:text-red-400"
                    disabled={bulkLoading}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {t('delete_selected')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Categories and Add Member Buttons */}
            <div className="flex space-x-3">
              <Button variant="outline" onClick={() => setShowCategoriesModal(true)}>
                <Tags className="w-4 h-4 mr-2" />
                Categories
              </Button>

              {/* Test Data Buttons - Only show in development */}
              {process.env.NODE_ENV === 'development' && (
                <>
                  <Button variant="outline" onClick={handleCreateTestData} className="text-blue-600">
                    Create Test Data
                  </Button>
                  <Button variant="outline" onClick={handleClearTestData} className="text-red-600">
                    Clear Data
                  </Button>
                </>
              )}

              <Button variant="gym" onClick={() => setShowAddModal(true)}>
                <UserPlus className="w-4 h-4 mr-2" />
                {t('add_new_member')}
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-base font-bold text-gray-600 dark:text-gray-400">Total Members</p>
                  <p className="text-2xl font-black text-gray-900 dark:text-white">{members.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center">
                  <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-base font-bold text-gray-600 dark:text-gray-400">Active</p>
                  <p className="text-2xl font-black text-gray-900 dark:text-white">
                    {members.filter(m => getMemberStatus(m) === 'active').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center">
                  <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <p className="text-base font-bold text-gray-600 dark:text-gray-400">Expiring</p>
                  <p className="text-2xl font-black text-gray-900 dark:text-white">
                    {members.filter(m => getMemberStatus(m) === 'expiring').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                  <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <p className="text-base font-bold text-gray-600 dark:text-gray-400">Expired</p>
                  <p className="text-2xl font-black text-gray-900 dark:text-white">
                    {members.filter(m => getMemberStatus(m) === 'expired').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center">
                  <CreditCard className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <p className="text-base font-bold text-gray-600 dark:text-gray-400">With Credit</p>
                  <p className="text-2xl font-black text-gray-900 dark:text-white">
                    {members.filter(m => (m.credit_limit || 0) > 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="glass border-white/20">
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Bulk Select */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSelectAll}
                  className="flex items-center space-x-2"
                >
                  {selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? (
                    <CheckSquare className="w-4 h-4" />
                  ) : (
                    <Square className="w-4 h-4" />
                  )}
                  <span className="text-sm">
                    {selectedMembers.size > 0 ? `${selectedMembers.size} ${t('selected_count')}` : t('select_all')}
                  </span>
                </Button>
              </div>

              {/* Search */}
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder={t('search_members')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
              </div>

              {/* Status Filter */}
              <div className="flex space-x-2">
                {['all', 'active', 'expiring', 'expired'].map((status) => (
                  <Button
                    key={status}
                    variant={statusFilter === status ? 'gym' : 'outline'}
                    size="sm"
                    onClick={() => setStatusFilter(status as any)}
                    className="capitalize"
                  >
                    {t(status)}
                  </Button>
                ))}
              </div>

              {/* Credit Filter */}
              <div className="flex space-x-2">
                {[
                  { key: 'all', label: 'All Credit' },
                  { key: 'has_credit', label: 'Has Credit' }
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={creditFilter === filter.key ? 'gym' : 'outline'}
                    size="sm"
                    onClick={() => setCreditFilter(filter.key as any)}
                    className="text-xs"
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Members Table */}
        <Card className="glass border-white/20">
          <CardContent className="p-0">
            <MembersTable
              members={filteredMembers}
              loading={loading}
              selectedMembers={selectedMembers}
              onSelectionChange={setSelectedMembers}
              onEdit={handleEditMember}
              onDelete={deleteMember}
              onViewDetails={handleViewDetails}
              onPrintReport={handlePrintReport}
              onMemberUpdated={fetchMembers}
            />
          </CardContent>
        </Card>

        {filteredMembers.length === 0 && (
          <Card className="glass border-white/20">
            <CardContent className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('no_members_found')}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {searchQuery || statusFilter !== 'all'
                  ? t('try_adjusting_search')
                  : t('get_started_adding')}
              </p>
              <Button variant="gym" onClick={() => setShowAddModal(true)}>
                <Plus className="w-4 h-4 mr-2" />
                {t('add_new_member')}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Modals */}
        <AddMemberModal
          open={showAddModal}
          onOpenChange={setShowAddModal}
          onMemberAdded={fetchMembers}
        />

        <EditMemberModal
          open={showEditModal}
          onOpenChange={setShowEditModal}
          member={editingMember}
          onMemberUpdated={fetchMembers}
        />

        <ViewMemberModal
          open={showViewModal}
          onOpenChange={setShowViewModal}
          member={viewingMember}
          onMemberUpdated={fetchMembers}
        />

        <CategoriesModal
          open={showCategoriesModal}
          onOpenChange={setShowCategoriesModal}
          onSportsUpdated={() => {
            // Sports updated - could refresh sports list if needed
            toast({
              title: 'Sports Updated',
              description: 'Sports have been updated successfully',
            })
          }}
        />
      </div>
    </MainLayout>
  )
}
