"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 407,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 421,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 496,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 505,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 520,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 573,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 576,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 581,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 589,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 598,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 592,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 606,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 580,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 629,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 646,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 641,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 654,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 649,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 667,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 666,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 417,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 691,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 708,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 721,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 729,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 703,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 689,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 754,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 762,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 416,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});