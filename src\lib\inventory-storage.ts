// localStorage-based inventory management system

export interface Supplier {
  id: string
  name: string
  contact_person?: string
  phone?: string
  email?: string
  address?: string
  tax_number?: string
  payment_terms: 'cash' | 'credit_30' | 'credit_60' | 'credit_90'
  credit_limit: number
  current_balance: number // Outstanding balance for credit suppliers
  notes?: string
  active: boolean
  created_at: string
  updated_at: string
}

export interface Purchase {
  id: string
  supplier_id: string
  invoice_number?: string
  purchase_date: string
  payment_type: 'cash' | 'credit'
  total_amount: number
  paid_amount: number
  remaining_balance: number
  payment_status: 'paid' | 'partial' | 'unpaid'
  due_date?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface PurchaseItem {
  id: string
  purchase_id: string
  product_id: string
  product_name: string
  quantity: number
  unit_cost: number
  total_cost: number
  expiry_date?: string
  created_at: string
}

export interface SupplierPayment {
  id: string
  supplier_id: string
  purchase_id?: string
  payment_date: string
  amount: number
  payment_method: 'cash' | 'bank_transfer' | 'check' | 'card'
  reference_number?: string
  notes?: string
  created_at: string
}

export interface StockMovement {
  id: string
  product_id: string
  product_name: string
  movement_type: 'purchase' | 'sale' | 'adjustment' | 'return' | 'expired'
  quantity_change: number // Positive for additions, negative for reductions
  previous_stock: number
  new_stock: number
  reference_id?: string // Can reference purchase_id, sale_id, etc.
  reference_type?: string // 'purchase', 'sale', 'manual_adjustment', etc.
  notes?: string
  created_at: string
}

export interface Category {
  id: string
  name: string
  description?: string
  parent_id?: string
  active: boolean
  created_at: string
  updated_at: string
}

export interface ExtendedProduct {
  id: string
  name: string
  category: string
  price_dzd: number
  purchase_price_dzd?: number // Prix d'achat
  stock: number
  min_stock: number
  expiry_date?: string
  image_url?: string
  barcode?: string
  qr_code?: string
  description?: string
  brand?: string
  unit: string
  created_at: string
  updated_at: string
}

// localStorage keys
const STORAGE_KEYS = {
  SUPPLIERS: 'gym_suppliers',
  PURCHASES: 'gym_purchases',
  PURCHASE_ITEMS: 'gym_purchase_items',
  SUPPLIER_PAYMENTS: 'gym_supplier_payments',
  STOCK_MOVEMENTS: 'gym_stock_movements',
  CATEGORIES: 'gym_categories',
  PRODUCTS: 'gym_products', // Extended product structure
} as const

// Utility functions for localStorage operations
export class InventoryStorage {
  // Generic storage operations
  static getFromStorage<T>(key: string): T[] {
    try {
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error)
      return []
    }
  }

  static saveToStorage<T>(key: string, data: T[]): void {
    try {
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error(`Error saving ${key} to localStorage:`, error)
    }
  }

  private static generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // Supplier operations
  static getSuppliers(): Supplier[] {
    return this.getFromStorage<Supplier>(STORAGE_KEYS.SUPPLIERS)
  }

  static saveSuppliers(suppliers: Supplier[]): void {
    this.saveToStorage(STORAGE_KEYS.SUPPLIERS, suppliers)
  }

  static addSupplier(supplier: Omit<Supplier, 'id' | 'created_at' | 'updated_at'>): Supplier {
    const suppliers = this.getSuppliers()
    const newSupplier: Supplier = {
      ...supplier,
      id: this.generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    suppliers.push(newSupplier)
    this.saveSuppliers(suppliers)
    return newSupplier
  }

  static updateSupplier(id: string, updates: Partial<Supplier>): Supplier | null {
    const suppliers = this.getSuppliers()
    const index = suppliers.findIndex(s => s.id === id)
    if (index === -1) return null

    suppliers[index] = {
      ...suppliers[index],
      ...updates,
      updated_at: new Date().toISOString(),
    }
    this.saveSuppliers(suppliers)
    return suppliers[index]
  }

  static deleteSupplier(id: string): boolean {
    const suppliers = this.getSuppliers()
    const index = suppliers.findIndex(s => s.id === id)
    if (index === -1) return false

    suppliers[index].active = false
    this.saveSuppliers(suppliers)
    return true
  }

  static getSupplierById(id: string): Supplier | null {
    const suppliers = this.getSuppliers()
    return suppliers.find(s => s.id === id) || null
  }

  // Purchase operations
  static getPurchases(): Purchase[] {
    return this.getFromStorage<Purchase>(STORAGE_KEYS.PURCHASES)
  }

  static savePurchases(purchases: Purchase[]): void {
    this.saveToStorage(STORAGE_KEYS.PURCHASES, purchases)
  }

  static addPurchase(purchase: Omit<Purchase, 'id' | 'created_at' | 'updated_at'>): Purchase {
    const purchases = this.getPurchases()
    const newPurchase: Purchase = {
      ...purchase,
      id: this.generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    purchases.push(newPurchase)
    this.savePurchases(purchases)

    // Update supplier balance if credit purchase
    if (purchase.payment_type === 'credit') {
      this.updateSupplierBalance(purchase.supplier_id, purchase.total_amount)
    }

    return newPurchase
  }

  static updatePurchase(id: string, updates: Partial<Purchase>): Purchase | null {
    const purchases = this.getPurchases()
    const index = purchases.findIndex(p => p.id === id)
    if (index === -1) return null

    purchases[index] = {
      ...purchases[index],
      ...updates,
      updated_at: new Date().toISOString(),
    }
    this.savePurchases(purchases)
    return purchases[index]
  }

  // Purchase Items operations
  static getPurchaseItems(): PurchaseItem[] {
    return this.getFromStorage<PurchaseItem>(STORAGE_KEYS.PURCHASE_ITEMS)
  }

  static savePurchaseItems(items: PurchaseItem[]): void {
    this.saveToStorage(STORAGE_KEYS.PURCHASE_ITEMS, items)
  }

  static addPurchaseItem(item: Omit<PurchaseItem, 'id' | 'created_at'>): PurchaseItem {
    const items = this.getPurchaseItems()
    const newItem: PurchaseItem = {
      ...item,
      id: this.generateId(),
      created_at: new Date().toISOString(),
    }
    items.push(newItem)
    this.savePurchaseItems(items)

    // Update product stock
    this.updateProductStock(item.product_id, item.quantity, 'purchase', newItem.id)

    return newItem
  }

  static getPurchaseItemsByPurchaseId(purchaseId: string): PurchaseItem[] {
    const items = this.getPurchaseItems()
    return items.filter(item => item.purchase_id === purchaseId)
  }

  // Supplier Payment operations
  static getSupplierPayments(): SupplierPayment[] {
    return this.getFromStorage<SupplierPayment>(STORAGE_KEYS.SUPPLIER_PAYMENTS)
  }

  static saveSupplierPayments(payments: SupplierPayment[]): void {
    this.saveToStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS, payments)
  }

  static addSupplierPayment(payment: Omit<SupplierPayment, 'id' | 'created_at'>): SupplierPayment {
    const payments = this.getSupplierPayments()
    const newPayment: SupplierPayment = {
      ...payment,
      id: this.generateId(),
      created_at: new Date().toISOString(),
    }
    payments.push(newPayment)
    this.saveSupplierPayments(payments)

    // Update supplier balance
    this.updateSupplierBalance(payment.supplier_id, -payment.amount)

    // Update purchase payment status if purchase_id provided
    if (payment.purchase_id) {
      this.updatePurchasePaymentStatus(payment.purchase_id, payment.amount)
    }

    return newPayment
  }

  static getSupplierPaymentsBySupplier(supplierId: string): SupplierPayment[] {
    const payments = this.getSupplierPayments()
    return payments.filter(payment => payment.supplier_id === supplierId)
  }

  // Helper methods
  private static updateSupplierBalance(supplierId: string, amount: number): void {
    const suppliers = this.getSuppliers()
    const index = suppliers.findIndex(s => s.id === supplierId)
    if (index !== -1) {
      suppliers[index].current_balance += amount
      suppliers[index].updated_at = new Date().toISOString()
      this.saveSuppliers(suppliers)
    }
  }

  private static updatePurchasePaymentStatus(purchaseId: string, paymentAmount: number): void {
    const purchases = this.getPurchases()
    const index = purchases.findIndex(p => p.id === purchaseId)
    if (index !== -1) {
      const purchase = purchases[index]
      purchase.paid_amount += paymentAmount
      purchase.remaining_balance = purchase.total_amount - purchase.paid_amount
      
      if (purchase.remaining_balance <= 0) {
        purchase.payment_status = 'paid'
      } else if (purchase.paid_amount > 0) {
        purchase.payment_status = 'partial'
      }
      
      purchase.updated_at = new Date().toISOString()
      this.savePurchases(purchases)
    }
  }

  private static updateProductStock(productId: string, quantity: number, movementType: StockMovement['movement_type'], referenceId: string): void {
    // Get current products (assuming they're stored in localStorage)
    const products = this.getFromStorage<ExtendedProduct>(STORAGE_KEYS.PRODUCTS)
    const productIndex = products.findIndex(p => p.id === productId)
    
    if (productIndex !== -1) {
      const product = products[productIndex]
      const previousStock = product.stock
      const newStock = previousStock + quantity
      
      // Update product stock
      product.stock = newStock
      product.updated_at = new Date().toISOString()
      this.saveToStorage(STORAGE_KEYS.PRODUCTS, products)
      
      // Create stock movement record
      this.addStockMovement({
        product_id: productId,
        product_name: product.name,
        movement_type: movementType,
        quantity_change: quantity,
        previous_stock: previousStock,
        new_stock: newStock,
        reference_id: referenceId,
        reference_type: movementType,
        notes: `Stock ${movementType} - ${quantity} units`,
      })
    }
  }

  // Stock Movement operations
  static getStockMovements(): StockMovement[] {
    return this.getFromStorage<StockMovement>(STORAGE_KEYS.STOCK_MOVEMENTS)
  }

  static saveStockMovements(movements: StockMovement[]): void {
    this.saveToStorage(STORAGE_KEYS.STOCK_MOVEMENTS, movements)
  }

  static addStockMovement(movement: Omit<StockMovement, 'id' | 'created_at'>): StockMovement {
    const movements = this.getStockMovements()
    const newMovement: StockMovement = {
      ...movement,
      id: this.generateId(),
      created_at: new Date().toISOString(),
    }
    movements.push(newMovement)
    this.saveStockMovements(movements)
    return newMovement
  }

  static getStockMovementsByProduct(productId: string): StockMovement[] {
    const movements = this.getStockMovements()
    return movements.filter(movement => movement.product_id === productId)
  }

  // Category operations
  static getCategories(): Category[] {
    return this.getFromStorage<Category>(STORAGE_KEYS.CATEGORIES)
  }

  static saveCategories(categories: Category[]): void {
    this.saveToStorage(STORAGE_KEYS.CATEGORIES, categories)
  }

  static addCategory(category: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Category {
    const categories = this.getCategories()
    const newCategory: Category = {
      ...category,
      id: this.generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }
    categories.push(newCategory)
    this.saveCategories(categories)
    return newCategory
  }

  // Initialize default data
  static initializeDefaultData(): void {
    // Initialize default categories if none exist
    const categories = this.getCategories()
    if (categories.length === 0) {
      const defaultCategories = [
        { name: 'Supplements', description: 'Protein powders, vitamins, and nutritional supplements' },
        { name: 'Beverages', description: 'Energy drinks, water, sports drinks' },
        { name: 'Equipment', description: 'Gym equipment, accessories, and gear' },
        { name: 'Apparel', description: 'Gym clothing, shoes, and accessories' },
        { name: 'Snacks', description: 'Protein bars, healthy snacks' },
        { name: 'Personal Care', description: 'Towels, toiletries, hygiene products' },
        { name: 'Accessories', description: 'Gloves, belts, straps, and other accessories' },
        { name: 'Recovery', description: 'Recovery tools, massage equipment' },
      ]

      defaultCategories.forEach(cat => {
        this.addCategory({ ...cat, active: true })
      })
    }

    // Initialize sample suppliers if none exist
    const suppliers = this.getSuppliers()
    if (suppliers.length === 0) {
      const defaultSuppliers = [
        {
          name: 'Nutrition Plus',
          contact_person: 'Ahmed Benali',
          phone: '+213 555 123 456',
          email: '<EMAIL>',
          address: 'Zone Industrielle, Alger',
          payment_terms: 'credit_30' as const,
          credit_limit: 50000,
          current_balance: 0,
          notes: 'Main supplier for supplements and nutrition products',
          active: true,
        },
        {
          name: 'Sports Equipment DZ',
          contact_person: 'Fatima Khelil',
          phone: '+213 555 789 012',
          email: '<EMAIL>',
          address: 'Rue des Sports, Oran',
          payment_terms: 'cash' as const,
          credit_limit: 0,
          current_balance: 0,
          notes: 'Gym equipment and accessories supplier',
          active: true,
        },
        {
          name: 'Beverage Distributors',
          contact_person: 'Mohamed Saidi',
          phone: '+213 555 345 678',
          email: '<EMAIL>',
          address: 'Zone Commerciale, Constantine',
          payment_terms: 'credit_60' as const,
          credit_limit: 30000,
          current_balance: 0,
          notes: 'Energy drinks and beverages supplier',
          active: true,
        },
      ]

      defaultSuppliers.forEach(supplier => {
        this.addSupplier(supplier)
      })
    }

    // Initialize sample products if none exist
    const products = this.getFromStorage<ExtendedProduct>(STORAGE_KEYS.PRODUCTS)
    if (products.length === 0) {
      const defaultProducts = [
        {
          name: 'Whey Protein Powder',
          category: 'Supplements',
          price_dzd: 5500,
          stock: 25,
          min_stock: 5,
          expiry_date: '2025-12-31',
          barcode: '1234567890123',
          description: 'High-quality whey protein for muscle building',
          brand: 'Elite Nutrition',
          unit: 'piece',
        },
        {
          name: 'Energy Drink',
          category: 'Beverages',
          price_dzd: 250,
          stock: 50,
          min_stock: 10,
          expiry_date: '2024-08-30',
          barcode: '2345678901234',
          description: 'Energy boost for intense workouts',
          brand: 'Power Up',
          unit: 'bottle',
        },
        {
          name: 'Gym Towel',
          category: 'Accessories',
          price_dzd: 800,
          stock: 15,
          min_stock: 3,
          barcode: '3456789012345',
          description: 'High-quality microfiber gym towel',
          brand: 'Gym Elite',
          unit: 'piece',
        },
        {
          name: 'Protein Bar',
          category: 'Snacks',
          price_dzd: 350,
          stock: 40,
          min_stock: 8,
          expiry_date: '2024-10-15',
          barcode: '4567890123456',
          description: 'High-protein snack bar',
          brand: 'Fit Snacks',
          unit: 'piece',
        },
        {
          name: 'Pre-Workout Supplement',
          category: 'Supplements',
          price_dzd: 4200,
          stock: 12,
          min_stock: 3,
          expiry_date: '2025-06-30',
          barcode: '5678901234567',
          description: 'Pre-workout energy and focus supplement',
          brand: 'Elite Nutrition',
          unit: 'piece',
        },
        {
          name: 'Water Bottle',
          category: 'Accessories',
          price_dzd: 1200,
          stock: 30,
          min_stock: 5,
          barcode: '6789012345678',
          description: 'BPA-free sports water bottle',
          brand: 'Hydro Pro',
          unit: 'piece',
        },
      ]

      defaultProducts.forEach(product => {
        const newProduct: ExtendedProduct = {
          ...product,
          id: this.generateId(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
        products.push(newProduct)
      })

      this.saveToStorage(STORAGE_KEYS.PRODUCTS, products)
    }
  }
}
