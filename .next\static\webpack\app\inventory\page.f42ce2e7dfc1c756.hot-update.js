"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/inventory/stock-adjustment-modal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StockAdjustmentModal: function() { return /* binding */ StockAdjustmentModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ StockAdjustmentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StockAdjustmentModal(param) {\n    let { product, onClose, onSave } = param;\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [adjustmentType, setAdjustmentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const calculateNewStock = ()=>{\n        const qty = parseInt(quantity) || 0;\n        switch(adjustmentType){\n            case \"add\":\n                return product.stock + qty;\n            case \"subtract\":\n                return Math.max(0, product.stock - qty);\n            case \"set\":\n                return Math.max(0, qty);\n            default:\n                return product.stock;\n        }\n    };\n    const getQuantityChange = ()=>{\n        const newStock = calculateNewStock();\n        return newStock - product.stock;\n    };\n    const validateForm = ()=>{\n        if (!quantity || parseInt(quantity) < 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please enter a valid quantity\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (adjustmentType === \"subtract\" && parseInt(quantity) > product.stock) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Cannot subtract more than current stock\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!reason.trim()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please provide a reason for the adjustment\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            const newStock = calculateNewStock();\n            const quantityChange = getQuantityChange();\n            // Update product stock\n            const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n            const productIndex = products.findIndex((p)=>p.id === product.id);\n            if (productIndex !== -1) {\n                products[productIndex] = {\n                    ...products[productIndex],\n                    stock: newStock,\n                    updated_at: new Date().toISOString()\n                };\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n                // Create stock movement record\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.addStockMovement({\n                    product_id: product.id,\n                    product_name: product.name,\n                    movement_type: \"adjustment\",\n                    quantity_change: quantityChange,\n                    previous_stock: product.stock,\n                    new_stock: newStock,\n                    reference_type: \"manual_adjustment\",\n                    notes: \"\".concat(reason).concat(notes ? \" - \".concat(notes) : \"\")\n                });\n                toast({\n                    title: \"Success\",\n                    description: \"Stock adjustment saved successfully\"\n                });\n                onSave();\n                onClose();\n            }\n        } catch (error) {\n            console.error(\"Error saving stock adjustment:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save stock adjustment\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const adjustmentReasons = [\n        \"Physical count correction\",\n        \"Damaged goods\",\n        \"Expired products\",\n        \"Theft/Loss\",\n        \"Supplier return\",\n        \"Quality control\",\n        \"System error correction\",\n        \"Other\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        onClick: (e)=>{\n            if (e.target === e.currentTarget) {\n            // Don't close modal when clicking outside\n            }\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl glass border-white/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Stock Adjustment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onClose,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: product.image_url,\n                                        alt: product.name,\n                                        className: \"w-16 h-16 rounded-lg object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-8 h-8 text-gray-400 dark:text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: product.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4 text-gray-500 dark:text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                children: [\n                                                                    \"Current Stock: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900 dark:text-white\",\n                                                                        children: [\n                                                                            product.stock,\n                                                                            \" \",\n                                                                            product.unit\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 95\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.stock <= product.min_stock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-orange-500 dark:text-orange-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Low Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-900 dark:text-white mb-3\",\n                                    children: \"Adjustment Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"add\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"add\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"subtract\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"subtract\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtract Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"set\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"set\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Set Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: [\n                                        adjustmentType === \"add\" && \"Quantity to Add\",\n                                        adjustmentType === \"subtract\" && \"Quantity to Subtract\",\n                                        adjustmentType === \"set\" && \"New Stock Level\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: quantity,\n                                            onChange: (e)=>setQuantity(e.target.value),\n                                            className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Enter quantity\",\n                                            min: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                            children: product.unit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Current Stock:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        product.stock,\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                    children: \"Change:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getQuantityChange() >= 0 ? \"text-green-600 dark:text-green-400\" : \"text-red-600 dark:text-red-400\"),\n                                                    children: [\n                                                        getQuantityChange() >= 0 ? \"+\" : \"\",\n                                                        getQuantityChange(),\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-t border-gray-200 dark:border-gray-700 pt-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: \"New Stock:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-blue-600 dark:text-blue-400\",\n                                                    children: [\n                                                        calculateNewStock(),\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: \"Reason for Adjustment *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: reason,\n                                    onChange: (e)=>setReason(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: \"Select a reason\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        adjustmentReasons.map((reasonOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: reasonOption,\n                                                className: \"text-gray-900 dark:text-white\",\n                                                children: reasonOption\n                                            }, reasonOption, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-900 dark:text-white mb-2\",\n                                    children: \"Additional Notes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: notes,\n                                    onChange: (e)=>setNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500\",\n                                    rows: 3,\n                                    placeholder: \"Enter any additional notes about this adjustment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 pt-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSave,\n                                    disabled: isLoading || !quantity || !reason,\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        isLoading ? \"Saving...\" : \"Save Adjustment\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(StockAdjustmentModal, \"PoPOFcu/F4JVQSH0dZl7+OnvJAQ=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = StockAdjustmentModal;\nvar _c;\n$RefreshReg$(_c, \"StockAdjustmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx\n"));

/***/ })

});