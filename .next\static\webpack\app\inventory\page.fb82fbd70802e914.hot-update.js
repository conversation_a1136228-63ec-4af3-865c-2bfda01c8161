"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/lib/inventory-storage.ts":
/*!**************************************!*\
  !*** ./src/lib/inventory-storage.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryStorage: function() { return /* binding */ InventoryStorage; }\n/* harmony export */ });\n// localStorage-based inventory management system\n// localStorage keys\nconst STORAGE_KEYS = {\n    SUPPLIERS: \"gym_suppliers\",\n    PURCHASES: \"gym_purchases\",\n    PURCHASE_ITEMS: \"gym_purchase_items\",\n    SUPPLIER_PAYMENTS: \"gym_supplier_payments\",\n    STOCK_MOVEMENTS: \"gym_stock_movements\",\n    CATEGORIES: \"gym_categories\",\n    PRODUCTS: \"gym_products\"\n};\n// Utility functions for localStorage operations\nclass InventoryStorage {\n    // Generic storage operations\n    static getFromStorage(key) {\n        try {\n            const data = localStorage.getItem(key);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error(\"Error reading \".concat(key, \" from localStorage:\"), error);\n            return [];\n        }\n    }\n    static saveToStorage(key, data) {\n        try {\n            localStorage.setItem(key, JSON.stringify(data));\n        } catch (error) {\n            console.error(\"Error saving \".concat(key, \" to localStorage:\"), error);\n        }\n    }\n    static generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    // Supplier operations\n    static getSuppliers() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIERS);\n    }\n    static saveSuppliers(suppliers) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIERS, suppliers);\n    }\n    static addSupplier(supplier) {\n        const suppliers = this.getSuppliers();\n        const newSupplier = {\n            ...supplier,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        suppliers.push(newSupplier);\n        this.saveSuppliers(suppliers);\n        return newSupplier;\n    }\n    static updateSupplier(id, updates) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return null;\n        suppliers[index] = {\n            ...suppliers[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.saveSuppliers(suppliers);\n        return suppliers[index];\n    }\n    static deleteSupplier(id) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return false;\n        suppliers[index].active = false;\n        this.saveSuppliers(suppliers);\n        return true;\n    }\n    static getSupplierById(id) {\n        const suppliers = this.getSuppliers();\n        return suppliers.find((s)=>s.id === id) || null;\n    }\n    // Purchase operations\n    static getPurchases() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASES);\n    }\n    static savePurchases(purchases) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASES, purchases);\n    }\n    static addPurchase(purchase) {\n        const purchases = this.getPurchases();\n        const newPurchase = {\n            ...purchase,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        purchases.push(newPurchase);\n        this.savePurchases(purchases);\n        // Update supplier balance if credit purchase\n        if (purchase.payment_type === \"credit\") {\n            this.updateSupplierBalance(purchase.supplier_id, purchase.total_amount);\n        }\n        return newPurchase;\n    }\n    static updatePurchase(id, updates) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        purchases[index] = {\n            ...purchases[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.savePurchases(purchases);\n        return purchases[index];\n    }\n    // Purchase Items operations\n    static getPurchaseItems() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASE_ITEMS);\n    }\n    static savePurchaseItems(items) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASE_ITEMS, items);\n    }\n    static addPurchaseItem(item) {\n        const items = this.getPurchaseItems();\n        const newItem = {\n            ...item,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        items.push(newItem);\n        this.savePurchaseItems(items);\n        // Update product stock\n        this.updateProductStock(item.product_id, item.quantity, \"purchase\", newItem.id);\n        return newItem;\n    }\n    static getPurchaseItemsByPurchaseId(purchaseId) {\n        const items = this.getPurchaseItems();\n        return items.filter((item)=>item.purchase_id === purchaseId);\n    }\n    // Supplier Payment operations\n    static getSupplierPayments() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS);\n    }\n    static saveSupplierPayments(payments) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS, payments);\n    }\n    static addSupplierPayment(payment) {\n        const payments = this.getSupplierPayments();\n        const newPayment = {\n            ...payment,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        payments.push(newPayment);\n        this.saveSupplierPayments(payments);\n        // Update supplier balance\n        this.updateSupplierBalance(payment.supplier_id, -payment.amount);\n        // Update purchase payment status if purchase_id provided\n        if (payment.purchase_id) {\n            this.updatePurchasePaymentStatus(payment.purchase_id, payment.amount);\n        }\n        return newPayment;\n    }\n    static getSupplierPaymentsBySupplier(supplierId) {\n        const payments = this.getSupplierPayments();\n        return payments.filter((payment)=>payment.supplier_id === supplierId);\n    }\n    // Helper methods\n    static updateSupplierBalance(supplierId, amount) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === supplierId);\n        if (index !== -1) {\n            suppliers[index].current_balance += amount;\n            suppliers[index].updated_at = new Date().toISOString();\n            this.saveSuppliers(suppliers);\n        }\n    }\n    static updatePurchasePaymentStatus(purchaseId, paymentAmount) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === purchaseId);\n        if (index !== -1) {\n            const purchase = purchases[index];\n            purchase.paid_amount += paymentAmount;\n            purchase.remaining_balance = purchase.total_amount - purchase.paid_amount;\n            if (purchase.remaining_balance <= 0) {\n                purchase.payment_status = \"paid\";\n            } else if (purchase.paid_amount > 0) {\n                purchase.payment_status = \"partial\";\n            }\n            purchase.updated_at = new Date().toISOString();\n            this.savePurchases(purchases);\n        }\n    }\n    static updateProductStock(productId, quantity, movementType, referenceId) {\n        // Get current products (assuming they're stored in localStorage)\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        const productIndex = products.findIndex((p)=>p.id === productId);\n        if (productIndex !== -1) {\n            const product = products[productIndex];\n            const previousStock = product.stock;\n            const newStock = previousStock + quantity;\n            // Update product stock\n            product.stock = newStock;\n            product.updated_at = new Date().toISOString();\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n            // Create stock movement record\n            this.addStockMovement({\n                product_id: productId,\n                product_name: product.name,\n                movement_type: movementType,\n                quantity_change: quantity,\n                previous_stock: previousStock,\n                new_stock: newStock,\n                reference_id: referenceId,\n                reference_type: movementType,\n                notes: \"Stock \".concat(movementType, \" - \").concat(quantity, \" units\")\n            });\n        }\n    }\n    // Stock Movement operations\n    static getStockMovements() {\n        return this.getFromStorage(STORAGE_KEYS.STOCK_MOVEMENTS);\n    }\n    static saveStockMovements(movements) {\n        this.saveToStorage(STORAGE_KEYS.STOCK_MOVEMENTS, movements);\n    }\n    static addStockMovement(movement) {\n        const movements = this.getStockMovements();\n        const newMovement = {\n            ...movement,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        movements.push(newMovement);\n        this.saveStockMovements(movements);\n        return newMovement;\n    }\n    static getStockMovementsByProduct(productId) {\n        const movements = this.getStockMovements();\n        return movements.filter((movement)=>movement.product_id === productId);\n    }\n    // Category operations\n    static getCategories() {\n        return this.getFromStorage(STORAGE_KEYS.CATEGORIES);\n    }\n    static saveCategories(categories) {\n        this.saveToStorage(STORAGE_KEYS.CATEGORIES, categories);\n    }\n    static addCategory(category) {\n        const categories = this.getCategories();\n        const newCategory = {\n            ...category,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        categories.push(newCategory);\n        this.saveCategories(categories);\n        return newCategory;\n    }\n    // Initialize default data\n    static initializeDefaultData() {\n        // Initialize default categories if none exist\n        const categories = this.getCategories();\n        if (categories.length === 0) {\n            const defaultCategories = [\n                {\n                    name: \"Supplements\",\n                    description: \"Protein powders, vitamins, and nutritional supplements\"\n                },\n                {\n                    name: \"Beverages\",\n                    description: \"Energy drinks, water, sports drinks\"\n                },\n                {\n                    name: \"Equipment\",\n                    description: \"Gym equipment, accessories, and gear\"\n                },\n                {\n                    name: \"Apparel\",\n                    description: \"Gym clothing, shoes, and accessories\"\n                },\n                {\n                    name: \"Snacks\",\n                    description: \"Protein bars, healthy snacks\"\n                },\n                {\n                    name: \"Personal Care\",\n                    description: \"Towels, toiletries, hygiene products\"\n                },\n                {\n                    name: \"Accessories\",\n                    description: \"Gloves, belts, straps, and other accessories\"\n                },\n                {\n                    name: \"Recovery\",\n                    description: \"Recovery tools, massage equipment\"\n                }\n            ];\n            defaultCategories.forEach((cat)=>{\n                this.addCategory({\n                    ...cat,\n                    active: true\n                });\n            });\n        }\n        // Initialize sample suppliers if none exist\n        const suppliers = this.getSuppliers();\n        if (suppliers.length === 0) {\n            const defaultSuppliers = [\n                {\n                    name: \"Nutrition Plus\",\n                    contact_person: \"Ahmed Benali\",\n                    phone: \"+213 555 123 456\",\n                    email: \"<EMAIL>\",\n                    address: \"Zone Industrielle, Alger\",\n                    payment_terms: \"credit_30\",\n                    credit_limit: 50000,\n                    current_balance: 0,\n                    notes: \"Main supplier for supplements and nutrition products\",\n                    active: true\n                },\n                {\n                    name: \"Sports Equipment DZ\",\n                    contact_person: \"Fatima Khelil\",\n                    phone: \"+213 555 789 012\",\n                    email: \"<EMAIL>\",\n                    address: \"Rue des Sports, Oran\",\n                    payment_terms: \"cash\",\n                    credit_limit: 0,\n                    current_balance: 0,\n                    notes: \"Gym equipment and accessories supplier\",\n                    active: true\n                },\n                {\n                    name: \"Beverage Distributors\",\n                    contact_person: \"Mohamed Saidi\",\n                    phone: \"+213 555 345 678\",\n                    email: \"<EMAIL>\",\n                    address: \"Zone Commerciale, Constantine\",\n                    payment_terms: \"credit_60\",\n                    credit_limit: 30000,\n                    current_balance: 0,\n                    notes: \"Energy drinks and beverages supplier\",\n                    active: true\n                }\n            ];\n            defaultSuppliers.forEach((supplier)=>{\n                this.addSupplier(supplier);\n            });\n        }\n        // Initialize sample products if none exist\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        if (products.length === 0) {\n            const defaultProducts = [\n                {\n                    name: \"Whey Protein Powder\",\n                    category: \"Supplements\",\n                    price_dzd: 5500,\n                    stock: 25,\n                    min_stock: 5,\n                    expiry_date: \"2025-12-31\",\n                    barcode: \"1234567890123\",\n                    description: \"High-quality whey protein for muscle building\",\n                    brand: \"Elite Nutrition\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Energy Drink\",\n                    category: \"Beverages\",\n                    price_dzd: 250,\n                    stock: 50,\n                    min_stock: 10,\n                    expiry_date: \"2024-08-30\",\n                    barcode: \"2345678901234\",\n                    description: \"Energy boost for intense workouts\",\n                    brand: \"Power Up\",\n                    unit: \"bottle\"\n                },\n                {\n                    name: \"Gym Towel\",\n                    category: \"Accessories\",\n                    price_dzd: 800,\n                    stock: 15,\n                    min_stock: 3,\n                    barcode: \"3456789012345\",\n                    description: \"High-quality microfiber gym towel\",\n                    brand: \"Gym Elite\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Protein Bar\",\n                    category: \"Snacks\",\n                    price_dzd: 350,\n                    stock: 40,\n                    min_stock: 8,\n                    expiry_date: \"2024-10-15\",\n                    barcode: \"4567890123456\",\n                    description: \"High-protein snack bar\",\n                    brand: \"Fit Snacks\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Pre-Workout Supplement\",\n                    category: \"Supplements\",\n                    price_dzd: 4200,\n                    stock: 12,\n                    min_stock: 3,\n                    expiry_date: \"2025-06-30\",\n                    barcode: \"5678901234567\",\n                    description: \"Pre-workout energy and focus supplement\",\n                    brand: \"Elite Nutrition\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Water Bottle\",\n                    category: \"Accessories\",\n                    price_dzd: 1200,\n                    stock: 30,\n                    min_stock: 5,\n                    barcode: \"6789012345678\",\n                    description: \"BPA-free sports water bottle\",\n                    brand: \"Hydro Pro\",\n                    unit: \"piece\"\n                }\n            ];\n            defaultProducts.forEach((product)=>{\n                const newProduct = {\n                    ...product,\n                    id: this.generateId(),\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                };\n                products.push(newProduct);\n            });\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/inventory-storage.ts\n"));

/***/ })

});