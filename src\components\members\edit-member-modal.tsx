'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/components/providers'
import { formatCurrency, formatDate, getDaysUntilExpiry, getSubscriptionStatus, calculateEndDate, getAgeGroup } from '@/lib/utils'
import { useToast } from '@/hooks/use-toast'
import { MemberCreditStorage } from '@/lib/member-credit-storage'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Save,
  X,
  User,
  Plus,
  Trash2,
  RefreshCw,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface Subscription {
  id: string
  sport: string
  plan_type: 'monthly' | 'quarterly' | 'yearly'
  start_date: string
  end_date: string
  price_dzd: number
  status: 'active' | 'expiring' | 'expired'
}

interface SportsPricing {
  id: string
  sport: string
  gender: 'male' | 'female' | 'both'
  age_group: 'child' | 'adult' | 'senior' | 'all'
  monthly_price: number
  quarterly_price: number
  yearly_price: number
  pregnancy_allowed: boolean
}

interface Member {
  id: string
  full_name: string
  gender: 'male' | 'female'
  age: number
  phone: string
  email: string | null
  pregnant: boolean
  situation: string
  remarks: string | null
  created_at: string
  subscriptions: Subscription[]
  credit_limit?: number
  credit_balance?: number
}

interface EditMemberModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  member: Member | null
  onMemberUpdated: () => void
}

export function EditMemberModal({ open, onOpenChange, member, onMemberUpdated }: EditMemberModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    full_name: '',
    gender: 'male' as 'male' | 'female',
    age: '',
    phone: '',
    email: '',
    pregnant: false,
    situation: 'active',
    remarks: '',
    credit_limit: ''
  })
  const [sportsPricing, setSportsPricing] = useState<SportsPricing[]>([])
  const [availableSports, setAvailableSports] = useState<SportsPricing[]>([])
  const [showAddSubscription, setShowAddSubscription] = useState(false)
  const [newSubscription, setNewSubscription] = useState({
    sport: '',
    plan_type: 'monthly' as 'monthly' | 'quarterly' | 'yearly'
  })
  const [renewingSubscription, setRenewingSubscription] = useState<string | null>(null)
  const { t } = useLanguage()
  const { toast } = useToast()

  useEffect(() => {
    if (member && open) {
      // Get current credit limit from storage
      const memberCredit = MemberCreditStorage.getMemberCredit(member.id)

      setFormData({
        full_name: member.full_name,
        gender: member.gender,
        age: member.age.toString(),
        phone: member.phone,
        email: member.email || '',
        pregnant: member.pregnant,
        situation: member.situation,
        remarks: member.remarks || '',
        credit_limit: (memberCredit?.credit_limit || 0).toString()
      })
      fetchSportsPricing()
    }
  }, [member, open])

  useEffect(() => {
    filterAvailableSports()
  }, [sportsPricing, formData.gender, formData.age, formData.pregnant])

  const fetchSportsPricing = () => {
    try {
      const storedSports = localStorage.getItem('gym_sports')
      const data = storedSports ? JSON.parse(storedSports) : []
      setSportsPricing(data)
    } catch (error) {
      console.error('Error loading sports:', error)
      toast({
        title: 'Error',
        description: 'Failed to load sports pricing',
        variant: 'destructive',
      })
    }
  }

  const filterAvailableSports = () => {
    if (!formData.age) {
      setAvailableSports([])
      return
    }

    const ageGroup = getAgeGroup(parseInt(formData.age))

    const filtered = sportsPricing.filter(pricing => {
      const genderMatch = pricing.gender === 'both' || pricing.gender === formData.gender
      const ageMatch = pricing.age_group === 'all' || pricing.age_group === ageGroup
      const pregnancyMatch = !formData.pregnant || pricing.pregnancy_allowed

      return genderMatch && ageMatch && pregnancyMatch
    })

    setAvailableSports(filtered)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!member) return

    if (!formData.full_name.trim()) {
      toast({
        title: 'Error',
        description: 'Full name is required',
        variant: 'destructive',
      })
      return
    }

    if (!formData.phone.trim()) {
      toast({
        title: 'Error',
        description: 'Phone number is required',
        variant: 'destructive',
      })
      return
    }

    if (!formData.age || parseInt(formData.age) <= 0) {
      toast({
        title: 'Error',
        description: 'Valid age is required',
        variant: 'destructive',
      })
      return
    }

    try {
      setLoading(true)

      const updateData = {
        ...member,
        full_name: formData.full_name.trim(),
        gender: formData.gender,
        age: parseInt(formData.age),
        phone: formData.phone.trim(),
        email: formData.email.trim() || null,
        pregnant: formData.gender === 'female' ? formData.pregnant : false,
        situation: formData.situation,
        remarks: formData.remarks.trim() || null,
        updated_at: new Date().toISOString(),
      }

      // Update credit limit
      const creditLimit = parseFloat(formData.credit_limit) || 0
      MemberCreditStorage.setMemberCreditLimit(member.id, creditLimit)

      // Update in localStorage
      const storedUsers = localStorage.getItem('gym_members')
      const users = storedUsers ? JSON.parse(storedUsers) : []
      const updatedUsers = users.map((user: any) =>
        user.id === member.id ? updateData : user
      )
      localStorage.setItem('gym_members', JSON.stringify(updatedUsers))

      // Sync credit data with member records
      MemberCreditStorage.syncMemberCreditData()

      toast({
        title: 'Success',
        description: 'Member updated successfully',
      })

      onMemberUpdated()
      onOpenChange(false)
    } catch (error: any) {
      console.error('Error updating member:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to update member',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return (
          <Badge variant="default" className="bg-green-500 hover:bg-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        )
      case 'expiring':
        return (
          <Badge variant="secondary" className="bg-yellow-500 hover:bg-yellow-600 text-white">
            <Clock className="w-3 h-3 mr-1" />
            Expiring
          </Badge>
        )
      case 'expired':
        return (
          <Badge variant="destructive">
            <X className="w-3 h-3 mr-1" />
            Expired
          </Badge>
        )
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handleAddSubscription = () => {
    if (!newSubscription.sport) {
      toast({
        title: 'Error',
        description: 'Please select a sport',
        variant: 'destructive',
      })
      return
    }

    const selectedSport = availableSports.find(s => s.sport === newSubscription.sport)
    if (!selectedSport) {
      toast({
        title: 'Error',
        description: 'Selected sport not found',
        variant: 'destructive',
      })
      return
    }

    const startDate = new Date().toISOString().split('T')[0]
    const endDate = calculateEndDate(startDate, newSubscription.plan_type)

    let price = 0
    switch (newSubscription.plan_type) {
      case 'monthly':
        price = selectedSport.monthly_price
        break
      case 'quarterly':
        price = selectedSport.quarterly_price
        break
      case 'yearly':
        price = selectedSport.yearly_price
        break
    }

    const subscriptionData = {
      id: Date.now().toString(),
      user_id: member!.id,
      sport: newSubscription.sport,
      plan_type: newSubscription.plan_type,
      start_date: startDate,
      end_date: endDate,
      price_dzd: price,
      status: 'active' as 'active' | 'expiring' | 'expired',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    }

    // Save to localStorage
    const storedSubscriptions = localStorage.getItem('gym_subscriptions')
    const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
    subscriptions.push(subscriptionData)
    localStorage.setItem('gym_subscriptions', JSON.stringify(subscriptions))

    toast({
      title: 'Subscription Added',
      description: `${newSubscription.sport} subscription added successfully`,
    })

    setNewSubscription({ sport: '', plan_type: 'monthly' })
    setShowAddSubscription(false)
    onMemberUpdated()
  }

  const handleRenewSubscription = (subscription: Subscription) => {
    setRenewingSubscription(subscription.id)

    try {
      const startDate = new Date().toISOString().split('T')[0]
      const endDate = calculateEndDate(startDate, subscription.plan_type)

      const renewalData = {
        id: Date.now().toString(),
        user_id: member!.id,
        sport: subscription.sport,
        plan_type: subscription.plan_type,
        start_date: startDate,
        end_date: endDate,
        price_dzd: subscription.price_dzd,
        status: 'active' as 'active' | 'expiring' | 'expired',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      subscriptions.push(renewalData)
      localStorage.setItem('gym_subscriptions', JSON.stringify(subscriptions))

      toast({
        title: 'Subscription Renewed',
        description: `${subscription.sport} subscription renewed until ${endDate}`,
      })

      onMemberUpdated()
    } catch (error: any) {
      console.error('Error renewing subscription:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to renew subscription',
        variant: 'destructive',
      })
    } finally {
      setRenewingSubscription(null)
    }
  }

  const handleDeleteSubscription = (subscriptionId: string) => {
    if (!confirm('Are you sure you want to delete this subscription?')) return

    try {
      const storedSubscriptions = localStorage.getItem('gym_subscriptions')
      const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : []
      const updatedSubscriptions = subscriptions.filter((sub: any) => sub.id !== subscriptionId)
      localStorage.setItem('gym_subscriptions', JSON.stringify(updatedSubscriptions))

      toast({
        title: 'Subscription Deleted',
        description: 'Subscription has been deleted successfully',
      })

      onMemberUpdated()
    } catch (error: any) {
      console.error('Error deleting subscription:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete subscription',
        variant: 'destructive',
      })
    }
  }

  if (!member) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Edit Member - {member.full_name}</span>
          </DialogTitle>
          <DialogDescription>
            Update member information, manage subscriptions, and track status
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

          {/* Personal Information */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>
                Update basic member details
              </CardDescription>
            </CardHeader>
            <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Full Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    required
                    value={formData.full_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, full_name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter full name"
                  />
                </div>

                {/* Gender */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Gender *
                  </label>
                  <select
                    required
                    value={formData.gender}
                    onChange={(e) => setFormData(prev => ({ ...prev, gender: e.target.value as 'male' | 'female' }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </select>
                </div>

                {/* Age */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Age *
                  </label>
                  <input
                    type="number"
                    required
                    min="1"
                    max="120"
                    value={formData.age}
                    onChange={(e) => setFormData(prev => ({ ...prev, age: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter age"
                  />
                </div>

                {/* Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter phone number"
                  />
                </div>

                {/* Email */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter email address"
                  />
                </div>

                {/* Situation */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={formData.situation}
                    onChange={(e) => setFormData(prev => ({ ...prev, situation: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="suspended">Suspended</option>
                  </select>
                </div>

                {/* Credit Limit */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Credit Limit (DA)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="50"
                    value={formData.credit_limit}
                    onChange={(e) => setFormData(prev => ({ ...prev, credit_limit: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter credit limit (e.g., 600)"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Maximum amount this member can purchase on credit
                  </p>
                </div>
              </div>

              {/* Pregnancy checkbox for females */}
              {formData.gender === 'female' && (
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="pregnant"
                    checked={formData.pregnant}
                    onChange={(e) => setFormData(prev => ({ ...prev, pregnant: e.target.checked }))}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <label htmlFor="pregnant" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Currently pregnant
                  </label>
                </div>
              )}

              {/* Remarks */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Remarks
                </label>
                <textarea
                  value={formData.remarks}
                  onChange={(e) => setFormData(prev => ({ ...prev, remarks: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Enter any additional remarks"
                />
              </div>

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" variant="gym" disabled={loading}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="w-4 h-4 mr-2" />
                  )}
                  Update Member
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

          {/* Subscription Management */}
          <Card className="glass border-white/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>Subscription Management</span>
              </CardTitle>
              <CardDescription>
                Manage member subscriptions and renewals
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Current Subscriptions */}
              {member.subscriptions && member.subscriptions.length > 0 ? (
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">Current Subscriptions</h4>
                  {member.subscriptions.map((subscription) => {
                    const daysUntilExpiry = getDaysUntilExpiry(subscription.end_date)
                    const status = getSubscriptionStatus(subscription.end_date)

                    return (
                      <div key={subscription.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium text-gray-900 dark:text-white">
                              {subscription.sport}
                            </p>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {formatDate(subscription.start_date)} - {formatDate(subscription.end_date)}
                            </p>
                            <p className="text-xs text-gray-500">
                              {daysUntilExpiry < 0 ? `Expired ${Math.abs(daysUntilExpiry)} days ago` :
                               daysUntilExpiry === 0 ? 'Expires today' :
                               `${daysUntilExpiry} days remaining`}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium text-gray-900 dark:text-white">
                            {formatCurrency(subscription.price_dzd)}
                          </span>
                          {getStatusBadge(status)}
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRenewSubscription(subscription)}
                              disabled={renewingSubscription === subscription.id}
                            >
                              {renewingSubscription === subscription.id ? (
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                              ) : (
                                <RefreshCw className="w-3 h-3" />
                              )}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteSubscription(subscription.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-6">
                  <Activity className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600 dark:text-gray-400">No subscriptions found</p>
                </div>
              )}

              {/* Add New Subscription */}
              {!showAddSubscription ? (
                <Button
                  variant="outline"
                  onClick={() => setShowAddSubscription(true)}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add New Subscription
                </Button>
              ) : (
                <div className="space-y-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <h4 className="font-medium text-gray-900 dark:text-white">Add New Subscription</h4>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Sport
                      </label>
                      <select
                        value={newSubscription.sport}
                        onChange={(e) => setNewSubscription(prev => ({ ...prev, sport: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="">Select a sport</option>
                        {availableSports.map((sport) => (
                          <option key={sport.id} value={sport.sport}>
                            {sport.sport}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Type
                      </label>
                      <select
                        value={newSubscription.plan_type}
                        onChange={(e) => setNewSubscription(prev => ({ ...prev, plan_type: e.target.value as any }))}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                      >
                        <option value="monthly">Monthly</option>
                        <option value="quarterly">Quarterly</option>
                        <option value="yearly">Yearly</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="gym"
                      onClick={handleAddSubscription}
                      disabled={!newSubscription.sport}
                    >
                      Add Subscription
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowAddSubscription(false)
                        setNewSubscription({ sport: '', plan_type: 'monthly' })
                      }}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
