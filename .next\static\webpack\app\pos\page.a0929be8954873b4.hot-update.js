"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/member-credit-storage */ \"(app-pages-browser)/./src/lib/member-credit-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get product sales count for sorting\n    const getProductSalesCount = (productId)=>{\n        try {\n            const transactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            let salesCount = 0;\n            transactions.forEach((transaction)=>{\n                transaction.items.forEach((item)=>{\n                    var _products_find;\n                    if (item.name === ((_products_find = products.find((p)=>p.id === productId)) === null || _products_find === void 0 ? void 0 : _products_find.name)) {\n                        salesCount += item.quantity;\n                    }\n                });\n            });\n            return salesCount;\n        } catch (error) {\n            return 0;\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    }).sort((a, b)=>{\n        // Sort by sales count (popular products first), then by name\n        const aSales = getProductSalesCount(a.id);\n        const bSales = getProductSalesCount(b.id);\n        if (aSales !== bSales) {\n            return bSales - aSales // Higher sales first\n            ;\n        }\n        return a.name.localeCompare(b.name) // Alphabetical as secondary sort\n        ;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        // Validate credit limit for credit purchases\n        if (paymentType === \"credit\" && !validateCreditPurchase(member, getTotalAmount())) {\n            return; // Don't proceed if credit validation fails\n        }\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n        setShowPayment(true);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const validateCreditPurchase = (member, totalAmount)=>{\n        const creditCheck = _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.canMemberPurchaseOnCredit(member.id, totalAmount);\n        if (!creditCheck.canPurchase) {\n            if (creditCheck.creditLimit <= 0) {\n                toast({\n                    title: \"Credit Not Available\",\n                    description: \"\".concat(member.full_name, \" does not have a credit limit set.\"),\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"Credit Limit Exceeded\",\n                    description: \"Purchase amount (\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(totalAmount), \") would exceed \").concat(member.full_name, \"'s available credit (\").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit), \").\"),\n                    variant: \"destructive\"\n                });\n            }\n            return false;\n        }\n        // Warning if approaching credit limit\n        if (creditCheck.availableCredit - totalAmount < creditCheck.creditLimit * 0.1) {\n            toast({\n                title: \"Credit Limit Warning\",\n                description: \"\".concat(member.full_name, \" will have only \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit - totalAmount), \" credit remaining after this purchase.\"),\n                variant: \"default\"\n            });\n        }\n        return true;\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Handle credit transaction if payment type is credit\n            if (paymentType === \"credit\" && selectedMember && selectedMember.id !== \"guest\") {\n                const success = _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.updateMemberCreditBalance(selectedMember.id, selectedMember.full_name, getTotalAmount(), \"purchase\", transaction.id, \"POS Purchase - \".concat(cart.length, \" items\"));\n                if (!success) {\n                    throw new Error(\"Failed to process credit transaction\");\n                }\n                // Sync member credit data\n                _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.syncMemberCreditData();\n            }\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden\",\n                                                        children: [\n                                                            product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: product.image_url,\n                                                                alt: product.name,\n                                                                className: \"w-full h-full object-cover rounded-lg\",\n                                                                onError: (e)=>{\n                                                                    var _target_nextElementSibling;\n                                                                    // Fallback to placeholder if image fails to load\n                                                                    const target = e.target;\n                                                                    target.style.display = \"none\";\n                                                                    (_target_nextElementSibling = target.nextElementSibling) === null || _target_nextElementSibling === void 0 ? void 0 : _target_nextElementSibling.classList.remove(\"hidden\");\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 27\n                                                            }, this) : null,\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400 \".concat(product.image_url ? \"hidden\" : \"\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 609,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 619,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 623,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 583,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 576,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 646,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 664,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 665,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 676,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 691,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 685,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 670,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 723,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 719,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 737,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 750,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 745,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 758,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 718,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 641,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 772,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 771,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 770,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 797,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 803,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 796,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 795,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 809,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 817,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 816,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 808,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 841,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 833,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 824,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 793,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 858,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 866,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 507,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});