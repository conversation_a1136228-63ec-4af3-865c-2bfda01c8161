"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/inventory/page",{

/***/ "(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx":
/*!*************************************************************!*\
  !*** ./src/components/inventory/stock-adjustment-modal.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StockAdjustmentModal: function() { return /* binding */ StockAdjustmentModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Edit,Package,Save,TrendingDown,TrendingUp,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* __next_internal_client_entry_do_not_use__ StockAdjustmentModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction StockAdjustmentModal(param) {\n    let { product, onClose, onSave } = param;\n    _s();\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [adjustmentType, setAdjustmentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"add\");\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [reason, setReason] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const calculateNewStock = ()=>{\n        const qty = parseInt(quantity) || 0;\n        switch(adjustmentType){\n            case \"add\":\n                return product.stock + qty;\n            case \"subtract\":\n                return Math.max(0, product.stock - qty);\n            case \"set\":\n                return Math.max(0, qty);\n            default:\n                return product.stock;\n        }\n    };\n    const getQuantityChange = ()=>{\n        const newStock = calculateNewStock();\n        return newStock - product.stock;\n    };\n    const validateForm = ()=>{\n        if (!quantity || parseInt(quantity) < 0) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please enter a valid quantity\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (adjustmentType === \"subtract\" && parseInt(quantity) > product.stock) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Cannot subtract more than current stock\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        if (!reason.trim()) {\n            toast({\n                title: \"Validation Error\",\n                description: \"Please provide a reason for the adjustment\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        if (!validateForm()) return;\n        setIsLoading(true);\n        try {\n            const newStock = calculateNewStock();\n            const quantityChange = getQuantityChange();\n            // Update product stock\n            const products = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.getFromStorage(\"gym_products\");\n            const productIndex = products.findIndex((p)=>p.id === product.id);\n            if (productIndex !== -1) {\n                products[productIndex] = {\n                    ...products[productIndex],\n                    stock: newStock,\n                    updated_at: new Date().toISOString()\n                };\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.saveToStorage(\"gym_products\", products);\n                // Create stock movement record\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_6__.InventoryStorage.addStockMovement({\n                    product_id: product.id,\n                    product_name: product.name,\n                    movement_type: \"adjustment\",\n                    quantity_change: quantityChange,\n                    previous_stock: product.stock,\n                    new_stock: newStock,\n                    reference_type: \"manual_adjustment\",\n                    notes: \"\".concat(reason).concat(notes ? \" - \".concat(notes) : \"\")\n                });\n                toast({\n                    title: \"Success\",\n                    description: \"Stock adjustment saved successfully\"\n                });\n                onSave();\n                onClose();\n            }\n        } catch (error) {\n            console.error(\"Error saving stock adjustment:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save stock adjustment\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const adjustmentReasons = [\n        \"Physical count correction\",\n        \"Damaged goods\",\n        \"Expired products\",\n        \"Theft/Loss\",\n        \"Supplier return\",\n        \"Quality control\",\n        \"System error correction\",\n        \"Other\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n        onClick: (e)=>{\n            if (e.target === e.currentTarget) {\n            // Don't close modal when clicking outside\n            }\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl glass border-white/20\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"flex flex-row items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Stock Adjustment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: onClose,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: product.image_url,\n                                        alt: product.name,\n                                        className: \"w-16 h-16 rounded-lg object-cover\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-8 h-8 text-gray-400 dark:text-gray-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: product.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: product.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-4 mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"w-4 h-4 text-gray-500 dark:text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 190,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                                children: [\n                                                                    \"Current Stock: \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        className: \"text-gray-900 dark:text-white\",\n                                                                        children: [\n                                                                            product.stock,\n                                                                            \" \",\n                                                                            product.unit\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                        lineNumber: 191,\n                                                                        columnNumber: 95\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    product.stock <= product.min_stock && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-orange-500 dark:text-orange-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Low Stock\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-900 dark:text-white mb-3\",\n                                    children: \"Adjustment Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"add\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"add\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Add Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"subtract\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"subtract\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subtract Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: adjustmentType === \"set\" ? \"default\" : \"outline\",\n                                            onClick: ()=>setAdjustmentType(\"set\"),\n                                            className: \"flex items-center justify-center space-x-2 h-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Set Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: [\n                                        adjustmentType === \"add\" && \"Quantity to Add\",\n                                        adjustmentType === \"subtract\" && \"Quantity to Subtract\",\n                                        adjustmentType === \"set\" && \"New Stock Level\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: quantity,\n                                            onChange: (e)=>setQuantity(e.target.value),\n                                            className: \"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                            placeholder: \"Enter quantity\",\n                                            min: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: product.unit\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this),\n                                quantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-3 bg-blue-50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Current Stock:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        product.stock,\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Change:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium \".concat(getQuantityChange() >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        getQuantityChange() >= 0 ? \"+\" : \"\",\n                                                        getQuantityChange(),\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center border-t pt-2 mt-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"New Stock:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-blue-600\",\n                                                    children: [\n                                                        calculateNewStock(),\n                                                        \" \",\n                                                        product.unit\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Reason for Adjustment *\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: reason,\n                                    onChange: (e)=>setReason(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"\",\n                                            children: \"Select a reason\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        adjustmentReasons.map((reasonOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: reasonOption,\n                                                children: reasonOption\n                                            }, reasonOption, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium mb-2\",\n                                    children: \"Additional Notes\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: notes,\n                                    onChange: (e)=>setNotes(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500\",\n                                    rows: 3,\n                                    placeholder: \"Enter any additional notes about this adjustment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-4 pt-6 border-t\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: handleSave,\n                                    disabled: isLoading || !quantity || !reason,\n                                    className: \"bg-blue-500 hover:bg-blue-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Edit_Package_Save_TrendingDown_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        isLoading ? \"Saving...\" : \"Save Adjustment\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n            lineNumber: 159,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\components\\\\inventory\\\\stock-adjustment-modal.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, this);\n}\n_s(StockAdjustmentModal, \"PoPOFcu/F4JVQSH0dZl7+OnvJAQ=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_4__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = StockAdjustmentModal;\nvar _c;\n$RefreshReg$(_c, \"StockAdjustmentModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/inventory/stock-adjustment-modal.tsx\n"));

/***/ })

});