"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _lib_test_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/test-data */ \"(app-pages-browser)/./src/lib/test-data.ts\");\n/* harmony import */ var _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/member-credit-storage */ \"(app-pages-browser)/./src/lib/member-credit-storage.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/members/view-member-modal */ \"(app-pages-browser)/./src/components/members/view-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [creditFilter, setCreditFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingMember, setViewingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            // Sync credit data with members\n            _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_9__.MemberCreditStorage.syncMemberCreditData();\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        // Credit filtering\n        if (creditFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const creditBalance = member.credit_balance || 0;\n                const creditLimit = member.credit_limit || 0;\n                switch(creditFilter){\n                    case \"has_credit\":\n                        return creditLimit > 0;\n                    case \"no_credit\":\n                        return creditLimit === 0;\n                    case \"over_limit\":\n                        return creditLimit > 0 && creditBalance > creditLimit * 0.8 // Over 80% of limit\n                        ;\n                    default:\n                        return true;\n                }\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent subscription (sorted by end date)\n        const sortedSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date).sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return sortedSubscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return \"expired\";\n        // Get the most recent subscription (active or most recent)\n        const sortedSubscriptions = subscriptions.sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        const latestSubscription = sortedSubscriptions[0];\n        if (!latestSubscription || !latestSubscription.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(latestSubscription.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleCreateTestData = ()=>{\n        (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.createTestMembers)();\n        toast({\n            title: \"Test Data Created\",\n            description: \"Test members with different subscription statuses have been created\"\n        });\n        fetchMembers();\n    };\n    const handleClearTestData = ()=>{\n        if (confirm(\"Are you sure you want to clear all data? This cannot be undone.\")) {\n            (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.clearTestData)();\n            toast({\n                title: \"Data Cleared\",\n                description: \"All test data has been removed\"\n            });\n            fetchMembers();\n        }\n    };\n    const handleViewDetails = (member)=>{\n        setViewingMember(member);\n        setShowViewModal(true);\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleCreateTestData,\n                                                    className: \"text-blue-600\",\n                                                    children: \"Create Test Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleClearTestData,\n                                                    className: \"text-red-600\",\n                                                    children: \"Clear Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_14__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport,\n                            onMemberUpdated: fetchMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 643,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 642,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 665,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 660,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_11__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_12__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_13__.ViewMemberModal, {\n                    open: showViewModal,\n                    onOpenChange: setShowViewModal,\n                    member: viewingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_15__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onSportsUpdated: ()=>{\n                        // Sports updated - could refresh sports list if needed\n                        toast({\n                            title: \"Sports Updated\",\n                            description: \"Sports have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 441,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"4ahXRpUQDAkICKOZtdVCDHuX7k4=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});