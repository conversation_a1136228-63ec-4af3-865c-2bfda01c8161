"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 411,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 410,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 409,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 429,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 438,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 507,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 525,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 494,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 562,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 561,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 578,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 583,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 600,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 594,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 608,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 602,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 568,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 621,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 630,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 631,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 648,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 643,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 540,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 539,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 669,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 684,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 668,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 715,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 714,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 706,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 739,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 731,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 722,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 692,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 691,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 756,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 764,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 418,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});