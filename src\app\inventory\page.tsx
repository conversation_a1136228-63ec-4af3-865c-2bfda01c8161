'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { InventoryStorage, ExtendedProduct, Supplier, StockMovement } from '@/lib/inventory-storage'
import { formatCurrency } from '@/lib/utils'
import {
  Package,
  Plus,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Users,
  ShoppingCart,
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Scan,
  Image as ImageIcon,
  FileText,
  DollarSign,
} from 'lucide-react'

// Import components (we'll create these)
import { AddProductModal } from '@/components/inventory/add-product-modal'
import { SuppliersModal } from '@/components/inventory/suppliers-modal'
import { PurchaseModal } from '@/components/inventory/purchase-modal'
import { StockMovementHistory } from '@/components/inventory/stock-movement-history'
import { StockAdjustmentModal } from '@/components/inventory/stock-adjustment-modal'
import { InventoryReports } from '@/components/inventory/inventory-reports'
import { ProductDetailsModal } from '@/components/inventory/product-details-modal'

export default function InventoryPage() {
  const { t } = useLanguage()
  const { toast } = useToast()

  // State management
  const [products, setProducts] = useState<ExtendedProduct[]>([])
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [showLowStock, setShowLowStock] = useState(false)
  const [showExpiring, setShowExpiring] = useState(false)

  // Modal states
  const [showAddProduct, setShowAddProduct] = useState(false)
  const [showSuppliers, setShowSuppliers] = useState(false)
  const [showPurchase, setShowPurchase] = useState(false)
  const [showStockHistory, setShowStockHistory] = useState(false)
  const [showStockAdjustment, setShowStockAdjustment] = useState(false)
  const [showReports, setShowReports] = useState(false)
  const [showProductDetails, setShowProductDetails] = useState(false)
  const [selectedProduct, setSelectedProduct] = useState<ExtendedProduct | null>(null)

  // Load data on component mount
  useEffect(() => {
    loadData()
    InventoryStorage.initializeDefaultData()
  }, [])

  const loadData = () => {
    try {
      const productsData = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
      const suppliersData = InventoryStorage.getSuppliers()
      const movementsData = InventoryStorage.getStockMovements()

      setProducts(productsData)
      setSuppliers(suppliersData.filter(s => s.active))
      setStockMovements(movementsData)
    } catch (error) {
      console.error('Error loading inventory data:', error)
      toast({
        title: 'Error',
        description: 'Failed to load inventory data',
        variant: 'destructive',
      })
    }
  }

  // Filter products based on search and filters
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand?.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    
    const matchesLowStock = !showLowStock || product.stock <= product.min_stock
    
    const matchesExpiring = !showExpiring || (
      product.expiry_date && 
      new Date(product.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    )

    return matchesSearch && matchesCategory && (!showLowStock || matchesLowStock) && (!showExpiring || matchesExpiring)
  })

  // Calculate statistics
  const stats = {
    totalProducts: products.length,
    totalValue: products.reduce((sum, p) => sum + (p.stock * p.price_dzd), 0),
    totalPurchaseValue: products.reduce((sum, p) => sum + (p.stock * (p.purchase_price_dzd || 0)), 0),
    lowStockCount: products.filter(p => p.stock <= p.min_stock).length,
    expiringCount: products.filter(p =>
      p.expiry_date && new Date(p.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    ).length,
    activeSuppliers: suppliers.length,
  }

  const categories = [...new Set(products.map(p => p.category))]

  const handleProductUpdate = () => {
    loadData()
    setSelectedProduct(null)
  }

  const handleDeleteProduct = (productId: string) => {
    if (confirm('Are you sure you want to delete this product?')) {
      const updatedProducts = products.filter(p => p.id !== productId)
      InventoryStorage.saveToStorage('gym_products', updatedProducts)
      loadData()
      toast({
        title: 'Success',
        description: 'Product deleted successfully',
      })
    }
  }

  const getStockStatus = (product: ExtendedProduct) => {
    if (product.stock === 0) return { status: 'out', color: 'text-red-500', bg: 'bg-red-50' }
    if (product.stock <= product.min_stock) return { status: 'low', color: 'text-orange-500', bg: 'bg-orange-50' }
    return { status: 'good', color: 'text-green-500', bg: 'bg-green-50' }
  }

  const getExpiryStatus = (expiryDate?: string) => {
    if (!expiryDate) return null
    
    const today = new Date()
    const expiry = new Date(expiryDate)
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysUntilExpiry < 0) return { status: 'expired', color: 'text-red-500', text: 'Expired' }
    if (daysUntilExpiry <= 7) return { status: 'expiring', color: 'text-orange-500', text: `${daysUntilExpiry} days` }
    if (daysUntilExpiry <= 30) return { status: 'warning', color: 'text-yellow-500', text: `${daysUntilExpiry} days` }
    return { status: 'good', color: 'text-green-500', text: `${daysUntilExpiry} days` }
  }

  return (
    <MainLayout title="Inventory Management">
      <div className="space-y-6">
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Package className="w-8 h-8 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-600">Total Products</p>
                  <p className="text-2xl font-bold">{stats.totalProducts}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-8 h-8 text-green-500" />
                <div>
                  <p className="text-sm text-gray-600">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-8 h-8 text-orange-500" />
                <div>
                  <p className="text-sm text-gray-600">Low Stock</p>
                  <p className="text-2xl font-bold">{stats.lowStockCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="w-8 h-8 text-red-500" />
                <div>
                  <p className="text-sm text-gray-600">Expiring Soon</p>
                  <p className="text-2xl font-bold">{stats.expiringCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Users className="w-8 h-8 text-purple-500" />
                <div>
                  <p className="text-sm text-gray-600">Suppliers</p>
                  <p className="text-2xl font-bold">{stats.activeSuppliers}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass border-white/20">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="w-8 h-8 text-yellow-500" />
                <div>
                  <p className="text-sm text-gray-600">Total D'achat</p>
                  <p className="text-2xl font-bold">{formatCurrency(stats.totalPurchaseValue)}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4">
          <Button 
            onClick={() => setShowAddProduct(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add New Product
          </Button>
          
          <Button 
            onClick={() => setShowPurchase(true)}
            className="bg-green-500 hover:bg-green-600 text-white"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            New Purchase
          </Button>
          
          <Button 
            onClick={() => setShowSuppliers(true)}
            className="bg-purple-500 hover:bg-purple-600 text-white"
          >
            <Users className="w-4 h-4 mr-2" />
            Manage Suppliers
          </Button>
          
          <Button
            onClick={() => setShowStockHistory(true)}
            variant="outline"
            className="border-gray-300"
          >
            <TrendingDown className="w-4 h-4 mr-2" />
            Stock History
          </Button>

          <Button
            onClick={() => setShowReports(true)}
            variant="outline"
            className="border-gray-300"
          >
            <FileText className="w-4 h-4 mr-2" />
            Reports
          </Button>
        </div>

        {/* Search and Filters */}
        <Card className="glass border-white/20">
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="relative flex-1 min-w-64">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              
              <Button
                variant={showLowStock ? "default" : "outline"}
                onClick={() => setShowLowStock(!showLowStock)}
                size="sm"
              >
                <AlertTriangle className="w-4 h-4 mr-2" />
                Low Stock
              </Button>
              
              <Button
                variant={showExpiring ? "default" : "outline"}
                onClick={() => setShowExpiring(!showExpiring)}
                size="sm"
              >
                <Calendar className="w-4 h-4 mr-2" />
                Expiring
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Products Table */}
        <Card className="glass border-white/20">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Products ({filteredProducts.length})</span>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Product</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Category</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Stock</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Selling Price</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Purchase Price</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Expiry</th>
                    <th className="text-left py-3 px-4 text-gray-900 dark:text-white">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => {
                    const stockStatus = getStockStatus(product)
                    const expiryStatus = getExpiryStatus(product.expiry_date)
                    
                    return (
                      <tr key={product.id} className="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            {product.image_url ? (
                              <img 
                                src={product.image_url} 
                                alt={product.name}
                                className="w-10 h-10 rounded-lg object-cover"
                              />
                            ) : (
                              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                <ImageIcon className="w-5 h-5 text-gray-400" />
                              </div>
                            )}
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{product.name}</p>
                              {product.brand && (
                                <p className="text-sm text-gray-500 dark:text-gray-400">{product.brand}</p>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full text-sm text-gray-900 dark:text-white">
                            {product.category}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <span className={`font-medium ${stockStatus.color}`}>
                              {product.stock} {product.unit}
                            </span>
                            {stockStatus.status !== 'good' && (
                              <AlertTriangle className="w-4 h-4 text-orange-500" />
                            )}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-gray-900 dark:text-white">{formatCurrency(product.price_dzd)}</span>
                        </td>
                        <td className="py-3 px-4">
                          <span className="text-gray-600 dark:text-gray-400">
                            {product.purchase_price_dzd ? formatCurrency(product.purchase_price_dzd) : '-'}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          {expiryStatus ? (
                            <span className={expiryStatus.color}>
                              {expiryStatus.text}
                            </span>
                          ) : (
                            <span className="text-gray-400 dark:text-gray-500">No expiry</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedProduct(product)
                                setShowProductDetails(true)
                              }}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedProduct(product)
                                setShowAddProduct(true)
                              }}
                            >
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedProduct(product)
                                setShowStockAdjustment(true)
                              }}
                              className="text-orange-500 hover:text-orange-700"
                              title="Adjust Stock"
                            >
                              <TrendingUp className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteProduct(product.id)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
              
              {filteredProducts.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  No products found matching your criteria
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Modals */}
      {showAddProduct && (
        <AddProductModal
          product={selectedProduct}
          onClose={() => {
            setShowAddProduct(false)
            setSelectedProduct(null)
          }}
          onSave={handleProductUpdate}
        />
      )}

      {showSuppliers && (
        <SuppliersModal
          onClose={() => setShowSuppliers(false)}
          onUpdate={loadData}
        />
      )}

      {showPurchase && (
        <PurchaseModal
          onClose={() => setShowPurchase(false)}
          onSave={handleProductUpdate}
          suppliers={suppliers}
          products={products}
        />
      )}

      {showStockHistory && (
        <StockMovementHistory
          onClose={() => setShowStockHistory(false)}
          movements={stockMovements}
          products={products}
        />
      )}

      {showStockAdjustment && selectedProduct && (
        <StockAdjustmentModal
          product={selectedProduct}
          onClose={() => {
            setShowStockAdjustment(false)
            setSelectedProduct(null)
          }}
          onSave={handleProductUpdate}
        />
      )}

      {showReports && (
        <InventoryReports
          onClose={() => setShowReports(false)}
        />
      )}

      {showProductDetails && selectedProduct && (
        <ProductDetailsModal
          product={selectedProduct}
          onClose={() => {
            setShowProductDetails(false)
            setSelectedProduct(null)
          }}
          onEdit={() => {
            setShowProductDetails(false)
            setShowAddProduct(true)
          }}
        />
      )}
    </MainLayout>
  )
}
