// Member credit management system

export interface MemberCredit {
  member_id: string
  credit_limit: number
  credit_balance: number
  updated_at: string
}

export interface CreditTransaction {
  id: string
  member_id: string
  member_name: string
  transaction_type: 'purchase' | 'payment' | 'adjustment'
  amount: number
  previous_balance: number
  new_balance: number
  reference_id?: string // POS transaction ID or payment ID
  notes?: string
  created_at: string
}

export class MemberCreditStorage {
  private static readonly STORAGE_KEYS = {
    MEMBER_CREDITS: 'gym_member_credits',
    CREDIT_TRANSACTIONS: 'gym_credit_transactions',
  }

  // Utility methods
  private static generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9)
  }

  private static getFromStorage<T>(key: string): T[] {
    try {
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error(`Error reading from localStorage key ${key}:`, error)
      return []
    }
  }

  private static saveToStorage<T>(key: string, data: T[]): void {
    try {
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error(`Error saving to localStorage key ${key}:`, error)
    }
  }

  // Member Credit operations
  static getMemberCredits(): MemberCredit[] {
    return this.getFromStorage<MemberCredit>(this.STORAGE_KEYS.MEMBER_CREDITS)
  }

  static saveMemberCredits(credits: MemberCredit[]): void {
    this.saveToStorage(this.STORAGE_KEYS.MEMBER_CREDITS, credits)
  }

  static getMemberCredit(memberId: string): MemberCredit | null {
    const credits = this.getMemberCredits()
    return credits.find(c => c.member_id === memberId) || null
  }

  static setMemberCreditLimit(memberId: string, creditLimit: number): void {
    const credits = this.getMemberCredits()
    const existingIndex = credits.findIndex(c => c.member_id === memberId)
    
    if (existingIndex !== -1) {
      credits[existingIndex] = {
        ...credits[existingIndex],
        credit_limit: creditLimit,
        updated_at: new Date().toISOString(),
      }
    } else {
      credits.push({
        member_id: memberId,
        credit_limit: creditLimit,
        credit_balance: 0,
        updated_at: new Date().toISOString(),
      })
    }
    
    this.saveMemberCredits(credits)
  }

  static updateMemberCreditBalance(
    memberId: string, 
    memberName: string,
    amount: number, 
    transactionType: 'purchase' | 'payment' | 'adjustment',
    referenceId?: string,
    notes?: string
  ): boolean {
    const credits = this.getMemberCredits()
    const creditIndex = credits.findIndex(c => c.member_id === memberId)
    
    if (creditIndex === -1) {
      // Create new credit record if doesn't exist
      credits.push({
        member_id: memberId,
        credit_limit: 0, // Default limit, should be set separately
        credit_balance: 0,
        updated_at: new Date().toISOString(),
      })
    }
    
    const currentCredit = credits.find(c => c.member_id === memberId)!
    const previousBalance = currentCredit.credit_balance
    const newBalance = transactionType === 'purchase' 
      ? previousBalance + amount 
      : transactionType === 'payment'
      ? Math.max(0, previousBalance - amount)
      : amount // For adjustments, set to exact amount
    
    // Check credit limit for purchases
    if (transactionType === 'purchase' && currentCredit.credit_limit > 0 && newBalance > currentCredit.credit_limit) {
      return false // Credit limit exceeded
    }
    
    // Update credit balance
    currentCredit.credit_balance = newBalance
    currentCredit.updated_at = new Date().toISOString()
    
    this.saveMemberCredits(credits)
    
    // Record transaction
    this.addCreditTransaction({
      member_id: memberId,
      member_name: memberName,
      transaction_type: transactionType,
      amount: amount,
      previous_balance: previousBalance,
      new_balance: newBalance,
      reference_id: referenceId,
      notes: notes,
    })
    
    return true
  }

  // Credit Transaction operations
  static getCreditTransactions(): CreditTransaction[] {
    return this.getFromStorage<CreditTransaction>(this.STORAGE_KEYS.CREDIT_TRANSACTIONS)
  }

  static saveCreditTransactions(transactions: CreditTransaction[]): void {
    this.saveToStorage(this.STORAGE_KEYS.CREDIT_TRANSACTIONS, transactions)
  }

  static addCreditTransaction(transaction: Omit<CreditTransaction, 'id' | 'created_at'>): CreditTransaction {
    const transactions = this.getCreditTransactions()
    const newTransaction: CreditTransaction = {
      ...transaction,
      id: this.generateId(),
      created_at: new Date().toISOString(),
    }
    transactions.push(newTransaction)
    this.saveCreditTransactions(transactions)
    return newTransaction
  }

  static getMemberCreditTransactions(memberId: string): CreditTransaction[] {
    const transactions = this.getCreditTransactions()
    return transactions.filter(t => t.member_id === memberId).sort((a, b) => 
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )
  }

  // Utility methods
  static canMemberPurchaseOnCredit(memberId: string, purchaseAmount: number): {
    canPurchase: boolean
    currentBalance: number
    creditLimit: number
    availableCredit: number
  } {
    const memberCredit = this.getMemberCredit(memberId)
    
    if (!memberCredit || memberCredit.credit_limit <= 0) {
      return {
        canPurchase: false,
        currentBalance: memberCredit?.credit_balance || 0,
        creditLimit: memberCredit?.credit_limit || 0,
        availableCredit: 0,
      }
    }
    
    const availableCredit = memberCredit.credit_limit - memberCredit.credit_balance
    const canPurchase = purchaseAmount <= availableCredit
    
    return {
      canPurchase,
      currentBalance: memberCredit.credit_balance,
      creditLimit: memberCredit.credit_limit,
      availableCredit,
    }
  }

  static initializeMemberCredit(memberId: string, creditLimit: number = 0): void {
    const existingCredit = this.getMemberCredit(memberId)
    if (!existingCredit) {
      this.setMemberCreditLimit(memberId, creditLimit)
    }
  }

  // Sync member credit data with member records
  static syncMemberCreditData(): void {
    try {
      const members = JSON.parse(localStorage.getItem('gym_members') || '[]')
      const credits = this.getMemberCredits()
      
      // Update members with credit information
      const updatedMembers = members.map((member: any) => {
        const memberCredit = credits.find(c => c.member_id === member.id)
        return {
          ...member,
          credit_limit: memberCredit?.credit_limit || 0,
          credit_balance: memberCredit?.credit_balance || 0,
        }
      })
      
      localStorage.setItem('gym_members', JSON.stringify(updatedMembers))
    } catch (error) {
      console.error('Error syncing member credit data:', error)
    }
  }
}
