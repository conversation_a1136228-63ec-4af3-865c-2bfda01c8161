"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/lib/inventory-storage.ts":
/*!**************************************!*\
  !*** ./src/lib/inventory-storage.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InventoryStorage: function() { return /* binding */ InventoryStorage; }\n/* harmony export */ });\n// localStorage-based inventory management system\n// localStorage keys\nconst STORAGE_KEYS = {\n    SUPPLIERS: \"gym_suppliers\",\n    PURCHASES: \"gym_purchases\",\n    PURCHASE_ITEMS: \"gym_purchase_items\",\n    SUPPLIER_PAYMENTS: \"gym_supplier_payments\",\n    STOCK_MOVEMENTS: \"gym_stock_movements\",\n    CATEGORIES: \"gym_categories\",\n    PRODUCTS: \"gym_products\"\n};\n// Utility functions for localStorage operations\nclass InventoryStorage {\n    // Generic storage operations\n    static getFromStorage(key) {\n        try {\n            const data = localStorage.getItem(key);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error(\"Error reading \".concat(key, \" from localStorage:\"), error);\n            return [];\n        }\n    }\n    static saveToStorage(key, data) {\n        try {\n            localStorage.setItem(key, JSON.stringify(data));\n        } catch (error) {\n            console.error(\"Error saving \".concat(key, \" to localStorage:\"), error);\n        }\n    }\n    static generateId() {\n        return Date.now().toString(36) + Math.random().toString(36).substr(2);\n    }\n    // Supplier operations\n    static getSuppliers() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIERS);\n    }\n    static saveSuppliers(suppliers) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIERS, suppliers);\n    }\n    static addSupplier(supplier) {\n        const suppliers = this.getSuppliers();\n        const newSupplier = {\n            ...supplier,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        suppliers.push(newSupplier);\n        this.saveSuppliers(suppliers);\n        return newSupplier;\n    }\n    static updateSupplier(id, updates) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return null;\n        suppliers[index] = {\n            ...suppliers[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.saveSuppliers(suppliers);\n        return suppliers[index];\n    }\n    static deleteSupplier(id) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === id);\n        if (index === -1) return false;\n        suppliers[index].active = false;\n        this.saveSuppliers(suppliers);\n        return true;\n    }\n    static getSupplierById(id) {\n        const suppliers = this.getSuppliers();\n        return suppliers.find((s)=>s.id === id) || null;\n    }\n    // Purchase operations\n    static getPurchases() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASES);\n    }\n    static savePurchases(purchases) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASES, purchases);\n    }\n    static addPurchase(purchase) {\n        const purchases = this.getPurchases();\n        const newPurchase = {\n            ...purchase,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        purchases.push(newPurchase);\n        this.savePurchases(purchases);\n        // Update supplier balance if credit purchase\n        if (purchase.payment_type === \"credit\") {\n            this.updateSupplierBalance(purchase.supplier_id, purchase.total_amount);\n        }\n        return newPurchase;\n    }\n    static updatePurchase(id, updates) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        purchases[index] = {\n            ...purchases[index],\n            ...updates,\n            updated_at: new Date().toISOString()\n        };\n        this.savePurchases(purchases);\n        return purchases[index];\n    }\n    // Purchase Items operations\n    static getPurchaseItems() {\n        return this.getFromStorage(STORAGE_KEYS.PURCHASE_ITEMS);\n    }\n    static savePurchaseItems(items) {\n        this.saveToStorage(STORAGE_KEYS.PURCHASE_ITEMS, items);\n    }\n    static addPurchaseItem(item) {\n        const items = this.getPurchaseItems();\n        const newItem = {\n            ...item,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        items.push(newItem);\n        this.savePurchaseItems(items);\n        // Update product stock\n        this.updateProductStock(item.product_id, item.quantity, \"purchase\", newItem.id);\n        return newItem;\n    }\n    static getPurchaseItemsByPurchaseId(purchaseId) {\n        const items = this.getPurchaseItems();\n        return items.filter((item)=>item.purchase_id === purchaseId);\n    }\n    // Supplier Payment operations\n    static getSupplierPayments() {\n        return this.getFromStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS);\n    }\n    static saveSupplierPayments(payments) {\n        this.saveToStorage(STORAGE_KEYS.SUPPLIER_PAYMENTS, payments);\n    }\n    static addSupplierPayment(payment) {\n        const payments = this.getSupplierPayments();\n        const newPayment = {\n            ...payment,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        payments.push(newPayment);\n        this.saveSupplierPayments(payments);\n        // Update supplier balance\n        this.updateSupplierBalance(payment.supplier_id, -payment.amount);\n        // Update purchase payment status if purchase_id provided\n        if (payment.purchase_id) {\n            this.updatePurchasePaymentStatus(payment.purchase_id, payment.amount);\n        }\n        return newPayment;\n    }\n    static getSupplierPaymentsBySupplier(supplierId) {\n        const payments = this.getSupplierPayments();\n        return payments.filter((payment)=>payment.supplier_id === supplierId);\n    }\n    // Helper methods\n    static updateSupplierBalance(supplierId, amount) {\n        const suppliers = this.getSuppliers();\n        const index = suppliers.findIndex((s)=>s.id === supplierId);\n        if (index !== -1) {\n            suppliers[index].current_balance += amount;\n            suppliers[index].updated_at = new Date().toISOString();\n            this.saveSuppliers(suppliers);\n        }\n    }\n    static updatePurchasePaymentStatus(purchaseId, paymentAmount) {\n        const purchases = this.getPurchases();\n        const index = purchases.findIndex((p)=>p.id === purchaseId);\n        if (index !== -1) {\n            const purchase = purchases[index];\n            purchase.paid_amount += paymentAmount;\n            purchase.remaining_balance = purchase.total_amount - purchase.paid_amount;\n            if (purchase.remaining_balance <= 0) {\n                purchase.payment_status = \"paid\";\n            } else if (purchase.paid_amount > 0) {\n                purchase.payment_status = \"partial\";\n            }\n            purchase.updated_at = new Date().toISOString();\n            this.savePurchases(purchases);\n        }\n    }\n    static updateProductStock(productId, quantity, movementType, referenceId) {\n        // Get current products (assuming they're stored in localStorage)\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        const productIndex = products.findIndex((p)=>p.id === productId);\n        if (productIndex !== -1) {\n            const product = products[productIndex];\n            const previousStock = product.stock;\n            const newStock = previousStock + quantity;\n            // Update product stock\n            product.stock = newStock;\n            product.updated_at = new Date().toISOString();\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n            // Create stock movement record\n            this.addStockMovement({\n                product_id: productId,\n                product_name: product.name,\n                movement_type: movementType,\n                quantity_change: quantity,\n                previous_stock: previousStock,\n                new_stock: newStock,\n                reference_id: referenceId,\n                reference_type: movementType,\n                notes: \"Stock \".concat(movementType, \" - \").concat(quantity, \" units\")\n            });\n        }\n    }\n    // Stock Movement operations\n    static getStockMovements() {\n        return this.getFromStorage(STORAGE_KEYS.STOCK_MOVEMENTS);\n    }\n    static saveStockMovements(movements) {\n        this.saveToStorage(STORAGE_KEYS.STOCK_MOVEMENTS, movements);\n    }\n    static addStockMovement(movement) {\n        const movements = this.getStockMovements();\n        const newMovement = {\n            ...movement,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        movements.push(newMovement);\n        this.saveStockMovements(movements);\n        return newMovement;\n    }\n    static getStockMovementsByProduct(productId) {\n        const movements = this.getStockMovements();\n        return movements.filter((movement)=>movement.product_id === productId);\n    }\n    // Category operations\n    static getCategories() {\n        return this.getFromStorage(STORAGE_KEYS.CATEGORIES);\n    }\n    static saveCategories(categories) {\n        this.saveToStorage(STORAGE_KEYS.CATEGORIES, categories);\n    }\n    static addCategory(category) {\n        const categories = this.getCategories();\n        const newCategory = {\n            ...category,\n            id: this.generateId(),\n            created_at: new Date().toISOString(),\n            updated_at: new Date().toISOString()\n        };\n        categories.push(newCategory);\n        this.saveCategories(categories);\n        return newCategory;\n    }\n    // Initialize default data\n    static initializeDefaultData() {\n        // Initialize default categories if none exist\n        const categories = this.getCategories();\n        if (categories.length === 0) {\n            const defaultCategories = [\n                {\n                    name: \"Supplements\",\n                    description: \"Protein powders, vitamins, and nutritional supplements\"\n                },\n                {\n                    name: \"Beverages\",\n                    description: \"Energy drinks, water, sports drinks\"\n                },\n                {\n                    name: \"Equipment\",\n                    description: \"Gym equipment, accessories, and gear\"\n                },\n                {\n                    name: \"Apparel\",\n                    description: \"Gym clothing, shoes, and accessories\"\n                },\n                {\n                    name: \"Snacks\",\n                    description: \"Protein bars, healthy snacks\"\n                },\n                {\n                    name: \"Personal Care\",\n                    description: \"Towels, toiletries, hygiene products\"\n                },\n                {\n                    name: \"Accessories\",\n                    description: \"Gloves, belts, straps, and other accessories\"\n                },\n                {\n                    name: \"Recovery\",\n                    description: \"Recovery tools, massage equipment\"\n                }\n            ];\n            defaultCategories.forEach((cat)=>{\n                this.addCategory({\n                    ...cat,\n                    active: true\n                });\n            });\n        }\n        // Initialize sample suppliers if none exist\n        const suppliers = this.getSuppliers();\n        if (suppliers.length === 0) {\n            const defaultSuppliers = [\n                {\n                    name: \"Nutrition Plus\",\n                    contact_person: \"Ahmed Benali\",\n                    phone: \"+213 555 123 456\",\n                    email: \"<EMAIL>\",\n                    address: \"Zone Industrielle, Alger\",\n                    payment_terms: \"credit_30\",\n                    credit_limit: 50000,\n                    current_balance: 0,\n                    notes: \"Main supplier for supplements and nutrition products\",\n                    active: true\n                },\n                {\n                    name: \"Sports Equipment DZ\",\n                    contact_person: \"Fatima Khelil\",\n                    phone: \"+213 555 789 012\",\n                    email: \"<EMAIL>\",\n                    address: \"Rue des Sports, Oran\",\n                    payment_terms: \"cash\",\n                    credit_limit: 0,\n                    current_balance: 0,\n                    notes: \"Gym equipment and accessories supplier\",\n                    active: true\n                },\n                {\n                    name: \"Beverage Distributors\",\n                    contact_person: \"Mohamed Saidi\",\n                    phone: \"+213 555 345 678\",\n                    email: \"<EMAIL>\",\n                    address: \"Zone Commerciale, Constantine\",\n                    payment_terms: \"credit_60\",\n                    credit_limit: 30000,\n                    current_balance: 0,\n                    notes: \"Energy drinks and beverages supplier\",\n                    active: true\n                }\n            ];\n            defaultSuppliers.forEach((supplier)=>{\n                this.addSupplier(supplier);\n            });\n        }\n        // Initialize sample products if none exist\n        const products = this.getFromStorage(STORAGE_KEYS.PRODUCTS);\n        if (products.length === 0) {\n            const defaultProducts = [\n                {\n                    name: \"Whey Protein Powder\",\n                    category: \"Supplements\",\n                    price_dzd: 5500,\n                    stock: 25,\n                    min_stock: 5,\n                    expiry_date: \"2025-12-31\",\n                    barcode: \"1234567890123\",\n                    description: \"High-quality whey protein for muscle building\",\n                    brand: \"Elite Nutrition\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Energy Drink\",\n                    category: \"Beverages\",\n                    price_dzd: 250,\n                    stock: 50,\n                    min_stock: 10,\n                    expiry_date: \"2024-08-30\",\n                    barcode: \"2345678901234\",\n                    description: \"Energy boost for intense workouts\",\n                    brand: \"Power Up\",\n                    unit: \"bottle\"\n                },\n                {\n                    name: \"Gym Towel\",\n                    category: \"Accessories\",\n                    price_dzd: 800,\n                    stock: 15,\n                    min_stock: 3,\n                    barcode: \"3456789012345\",\n                    description: \"High-quality microfiber gym towel\",\n                    brand: \"Gym Elite\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Protein Bar\",\n                    category: \"Snacks\",\n                    price_dzd: 350,\n                    stock: 40,\n                    min_stock: 8,\n                    expiry_date: \"2024-10-15\",\n                    barcode: \"4567890123456\",\n                    description: \"High-protein snack bar\",\n                    brand: \"Fit Snacks\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Pre-Workout Supplement\",\n                    category: \"Supplements\",\n                    price_dzd: 4200,\n                    stock: 12,\n                    min_stock: 3,\n                    expiry_date: \"2025-06-30\",\n                    barcode: \"5678901234567\",\n                    description: \"Pre-workout energy and focus supplement\",\n                    brand: \"Elite Nutrition\",\n                    unit: \"piece\"\n                },\n                {\n                    name: \"Water Bottle\",\n                    category: \"Accessories\",\n                    price_dzd: 1200,\n                    stock: 30,\n                    min_stock: 5,\n                    barcode: \"6789012345678\",\n                    description: \"BPA-free sports water bottle\",\n                    brand: \"Hydro Pro\",\n                    unit: \"piece\"\n                }\n            ];\n            defaultProducts.forEach((product)=>{\n                const newProduct = {\n                    ...product,\n                    id: this.generateId(),\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString()\n                };\n                products.push(newProduct);\n            });\n            this.saveToStorage(STORAGE_KEYS.PRODUCTS, products);\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/inventory-storage.ts\n"));

/***/ })

});