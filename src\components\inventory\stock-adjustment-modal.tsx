'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useLanguage } from '@/components/providers'
import { useToast } from '@/hooks/use-toast'
import { InventoryStorage, ExtendedProduct } from '@/lib/inventory-storage'
import {
  X,
  Save,
  Package,
  Plus,
  Minus,
  Edit,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
} from 'lucide-react'

interface StockAdjustmentModalProps {
  product: ExtendedProduct
  onClose: () => void
  onSave: () => void
}

export function StockAdjustmentModal({ product, onClose, onSave }: StockAdjustmentModalProps) {
  const { t } = useLanguage()
  const { toast } = useToast()

  const [adjustmentType, setAdjustmentType] = useState<'add' | 'subtract' | 'set'>('add')
  const [quantity, setQuantity] = useState('')
  const [reason, setReason] = useState('')
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const calculateNewStock = () => {
    const qty = parseInt(quantity) || 0
    switch (adjustmentType) {
      case 'add':
        return product.stock + qty
      case 'subtract':
        return Math.max(0, product.stock - qty)
      case 'set':
        return Math.max(0, qty)
      default:
        return product.stock
    }
  }

  const getQuantityChange = () => {
    const newStock = calculateNewStock()
    return newStock - product.stock
  }

  const validateForm = () => {
    if (!quantity || parseInt(quantity) < 0) {
      toast({
        title: 'Validation Error',
        description: 'Please enter a valid quantity',
        variant: 'destructive',
      })
      return false
    }

    if (adjustmentType === 'subtract' && parseInt(quantity) > product.stock) {
      toast({
        title: 'Validation Error',
        description: 'Cannot subtract more than current stock',
        variant: 'destructive',
      })
      return false
    }

    if (!reason.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Please provide a reason for the adjustment',
        variant: 'destructive',
      })
      return false
    }

    return true
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setIsLoading(true)
    try {
      const newStock = calculateNewStock()
      const quantityChange = getQuantityChange()

      // Update product stock
      const products = InventoryStorage.getFromStorage<ExtendedProduct>('gym_products')
      const productIndex = products.findIndex(p => p.id === product.id)
      
      if (productIndex !== -1) {
        products[productIndex] = {
          ...products[productIndex],
          stock: newStock,
          updated_at: new Date().toISOString(),
        }
        InventoryStorage.saveToStorage('gym_products', products)

        // Create stock movement record
        InventoryStorage.addStockMovement({
          product_id: product.id,
          product_name: product.name,
          movement_type: 'adjustment',
          quantity_change: quantityChange,
          previous_stock: product.stock,
          new_stock: newStock,
          reference_type: 'manual_adjustment',
          notes: `${reason}${notes ? ` - ${notes}` : ''}`,
        })

        toast({
          title: 'Success',
          description: 'Stock adjustment saved successfully',
        })

        onSave()
        onClose()
      }
    } catch (error) {
      console.error('Error saving stock adjustment:', error)
      toast({
        title: 'Error',
        description: 'Failed to save stock adjustment',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const adjustmentReasons = [
    'Physical count correction',
    'Damaged goods',
    'Expired products',
    'Theft/Loss',
    'Supplier return',
    'Quality control',
    'System error correction',
    'Other',
  ]

  return (
    <div
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          // Don't close modal when clicking outside
        }
      }}
    >
      <Card className="w-full max-w-2xl glass border-white/20">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Edit className="w-6 h-6" />
            <span>Stock Adjustment</span>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Product Information */}
          <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div className="flex items-center space-x-4">
              {product.image_url ? (
                <img
                  src={product.image_url}
                  alt={product.name}
                  className="w-16 h-16 rounded-lg object-cover"
                />
              ) : (
                <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <Package className="w-8 h-8 text-gray-400 dark:text-gray-500" />
                </div>
              )}
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{product.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{product.category}</p>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center space-x-2">
                    <Package className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">Current Stock: <strong className="text-gray-900 dark:text-white">{product.stock} {product.unit}</strong></span>
                  </div>
                  {product.stock <= product.min_stock && (
                    <div className="flex items-center space-x-1 text-orange-500 dark:text-orange-400">
                      <AlertTriangle className="w-4 h-4" />
                      <span className="text-sm">Low Stock</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Adjustment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-white mb-3">Adjustment Type</label>
            <div className="grid grid-cols-3 gap-4">
              <Button
                variant={adjustmentType === 'add' ? 'default' : 'outline'}
                onClick={() => setAdjustmentType('add')}
                className="flex items-center justify-center space-x-2 h-12"
              >
                <TrendingUp className="w-4 h-4" />
                <span>Add Stock</span>
              </Button>
              <Button
                variant={adjustmentType === 'subtract' ? 'default' : 'outline'}
                onClick={() => setAdjustmentType('subtract')}
                className="flex items-center justify-center space-x-2 h-12"
              >
                <TrendingDown className="w-4 h-4" />
                <span>Subtract Stock</span>
              </Button>
              <Button
                variant={adjustmentType === 'set' ? 'default' : 'outline'}
                onClick={() => setAdjustmentType('set')}
                className="flex items-center justify-center space-x-2 h-12"
              >
                <Edit className="w-4 h-4" />
                <span>Set Stock</span>
              </Button>
            </div>
          </div>

          {/* Quantity Input */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">
              {adjustmentType === 'add' && 'Quantity to Add'}
              {adjustmentType === 'subtract' && 'Quantity to Subtract'}
              {adjustmentType === 'set' && 'New Stock Level'}
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="Enter quantity"
                min="0"
              />
              <span className="text-sm text-gray-600 dark:text-gray-400">{product.unit}</span>
            </div>

            {quantity && (
              <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Current Stock:</span>
                  <span className="font-medium text-gray-900 dark:text-white">{product.stock} {product.unit}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Change:</span>
                  <span className={`font-medium ${getQuantityChange() >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                    {getQuantityChange() >= 0 ? '+' : ''}{getQuantityChange()} {product.unit}
                  </span>
                </div>
                <div className="flex justify-between items-center border-t border-gray-200 dark:border-gray-700 pt-2 mt-2">
                  <span className="text-sm font-medium text-gray-900 dark:text-white">New Stock:</span>
                  <span className="font-bold text-blue-600 dark:text-blue-400">{calculateNewStock()} {product.unit}</span>
                </div>
              </div>
            )}
          </div>

          {/* Reason */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">Reason for Adjustment *</label>
            <select
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              <option value="" className="text-gray-500 dark:text-gray-400">Select a reason</option>
              {adjustmentReasons.map(reasonOption => (
                <option key={reasonOption} value={reasonOption} className="text-gray-900 dark:text-white">
                  {reasonOption}
                </option>
              ))}
            </select>
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-900 dark:text-white mb-2">Additional Notes</label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Enter any additional notes about this adjustment"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading || !quantity || !reason}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save Adjustment'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
