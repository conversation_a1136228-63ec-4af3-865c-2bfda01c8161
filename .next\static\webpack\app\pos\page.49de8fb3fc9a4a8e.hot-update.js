"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/member-credit-storage */ \"(app-pages-browser)/./src/lib/member-credit-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const validateCreditPurchase = (member, totalAmount)=>{\n        const creditCheck = _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.canMemberPurchaseOnCredit(member.id, totalAmount);\n        if (!creditCheck.canPurchase) {\n            if (creditCheck.creditLimit <= 0) {\n                toast({\n                    title: \"Credit Not Available\",\n                    description: \"\".concat(member.full_name, \" does not have a credit limit set.\"),\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"Credit Limit Exceeded\",\n                    description: \"Purchase amount (\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(totalAmount), \") would exceed \").concat(member.full_name, \"'s available credit (\").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit), \").\"),\n                    variant: \"destructive\"\n                });\n            }\n            return false;\n        }\n        // Warning if approaching credit limit\n        if (creditCheck.availableCredit - totalAmount < creditCheck.creditLimit * 0.1) {\n            toast({\n                title: \"Credit Limit Warning\",\n                description: \"\".concat(member.full_name, \" will have only \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit - totalAmount), \" credit remaining after this purchase.\"),\n                variant: \"default\"\n            });\n        }\n        return true;\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 443,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 441,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 479,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 554,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 557,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 553,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 527,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 580,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 594,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 593,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 621,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 632,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 626,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 640,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 634,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 602,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 653,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 662,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 663,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 661,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 667,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 680,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 683,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 571,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 710,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 705,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 700,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 728,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 726,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 725,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 747,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 755,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 771,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 763,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 754,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 724,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 723,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 788,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/member-credit-storage.ts":
/*!******************************************!*\
  !*** ./src/lib/member-credit-storage.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MemberCreditStorage: function() { return /* binding */ MemberCreditStorage; }\n/* harmony export */ });\n// Member credit management system\nclass MemberCreditStorage {\n    // Utility methods\n    static generateId() {\n        return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n    }\n    static getFromStorage(key) {\n        try {\n            const data = localStorage.getItem(key);\n            return data ? JSON.parse(data) : [];\n        } catch (error) {\n            console.error(\"Error reading from localStorage key \".concat(key, \":\"), error);\n            return [];\n        }\n    }\n    static saveToStorage(key, data) {\n        try {\n            localStorage.setItem(key, JSON.stringify(data));\n        } catch (error) {\n            console.error(\"Error saving to localStorage key \".concat(key, \":\"), error);\n        }\n    }\n    // Member Credit operations\n    static getMemberCredits() {\n        return this.getFromStorage(this.STORAGE_KEYS.MEMBER_CREDITS);\n    }\n    static saveMemberCredits(credits) {\n        this.saveToStorage(this.STORAGE_KEYS.MEMBER_CREDITS, credits);\n    }\n    static getMemberCredit(memberId) {\n        const credits = this.getMemberCredits();\n        return credits.find((c)=>c.member_id === memberId) || null;\n    }\n    static setMemberCreditLimit(memberId, creditLimit) {\n        const credits = this.getMemberCredits();\n        const existingIndex = credits.findIndex((c)=>c.member_id === memberId);\n        if (existingIndex !== -1) {\n            credits[existingIndex] = {\n                ...credits[existingIndex],\n                credit_limit: creditLimit,\n                updated_at: new Date().toISOString()\n            };\n        } else {\n            credits.push({\n                member_id: memberId,\n                credit_limit: creditLimit,\n                credit_balance: 0,\n                updated_at: new Date().toISOString()\n            });\n        }\n        this.saveMemberCredits(credits);\n    }\n    static updateMemberCreditBalance(memberId, memberName, amount, transactionType, referenceId, notes) {\n        const credits = this.getMemberCredits();\n        const creditIndex = credits.findIndex((c)=>c.member_id === memberId);\n        if (creditIndex === -1) {\n            // Create new credit record if doesn't exist\n            credits.push({\n                member_id: memberId,\n                credit_limit: 0,\n                credit_balance: 0,\n                updated_at: new Date().toISOString()\n            });\n        }\n        const currentCredit = credits.find((c)=>c.member_id === memberId);\n        const previousBalance = currentCredit.credit_balance;\n        const newBalance = transactionType === \"purchase\" ? previousBalance + amount : transactionType === \"payment\" ? Math.max(0, previousBalance - amount) : amount // For adjustments, set to exact amount\n        ;\n        // Check credit limit for purchases\n        if (transactionType === \"purchase\" && currentCredit.credit_limit > 0 && newBalance > currentCredit.credit_limit) {\n            return false // Credit limit exceeded\n            ;\n        }\n        // Update credit balance\n        currentCredit.credit_balance = newBalance;\n        currentCredit.updated_at = new Date().toISOString();\n        this.saveMemberCredits(credits);\n        // Record transaction\n        this.addCreditTransaction({\n            member_id: memberId,\n            member_name: memberName,\n            transaction_type: transactionType,\n            amount: amount,\n            previous_balance: previousBalance,\n            new_balance: newBalance,\n            reference_id: referenceId,\n            notes: notes\n        });\n        return true;\n    }\n    // Credit Transaction operations\n    static getCreditTransactions() {\n        return this.getFromStorage(this.STORAGE_KEYS.CREDIT_TRANSACTIONS);\n    }\n    static saveCreditTransactions(transactions) {\n        this.saveToStorage(this.STORAGE_KEYS.CREDIT_TRANSACTIONS, transactions);\n    }\n    static addCreditTransaction(transaction) {\n        const transactions = this.getCreditTransactions();\n        const newTransaction = {\n            ...transaction,\n            id: this.generateId(),\n            created_at: new Date().toISOString()\n        };\n        transactions.push(newTransaction);\n        this.saveCreditTransactions(transactions);\n        return newTransaction;\n    }\n    static getMemberCreditTransactions(memberId) {\n        const transactions = this.getCreditTransactions();\n        return transactions.filter((t)=>t.member_id === memberId).sort((a, b)=>new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\n    }\n    // Utility methods\n    static canMemberPurchaseOnCredit(memberId, purchaseAmount) {\n        const memberCredit = this.getMemberCredit(memberId);\n        if (!memberCredit || memberCredit.credit_limit <= 0) {\n            return {\n                canPurchase: false,\n                currentBalance: (memberCredit === null || memberCredit === void 0 ? void 0 : memberCredit.credit_balance) || 0,\n                creditLimit: (memberCredit === null || memberCredit === void 0 ? void 0 : memberCredit.credit_limit) || 0,\n                availableCredit: 0\n            };\n        }\n        const availableCredit = memberCredit.credit_limit - memberCredit.credit_balance;\n        const canPurchase = purchaseAmount <= availableCredit;\n        return {\n            canPurchase,\n            currentBalance: memberCredit.credit_balance,\n            creditLimit: memberCredit.credit_limit,\n            availableCredit\n        };\n    }\n    static initializeMemberCredit(memberId) {\n        let creditLimit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        const existingCredit = this.getMemberCredit(memberId);\n        if (!existingCredit) {\n            this.setMemberCreditLimit(memberId, creditLimit);\n        }\n    }\n    // Sync member credit data with member records\n    static syncMemberCreditData() {\n        try {\n            const members = JSON.parse(localStorage.getItem(\"gym_members\") || \"[]\");\n            const credits = this.getMemberCredits();\n            // Update members with credit information\n            const updatedMembers = members.map((member)=>{\n                const memberCredit = credits.find((c)=>c.member_id === member.id);\n                return {\n                    ...member,\n                    credit_limit: (memberCredit === null || memberCredit === void 0 ? void 0 : memberCredit.credit_limit) || 0,\n                    credit_balance: (memberCredit === null || memberCredit === void 0 ? void 0 : memberCredit.credit_balance) || 0\n                };\n            });\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedMembers));\n        } catch (error) {\n            console.error(\"Error syncing member credit data:\", error);\n        }\n    }\n}\nMemberCreditStorage.STORAGE_KEYS = {\n    MEMBER_CREDITS: \"gym_member_credits\",\n    CREDIT_TRANSACTIONS: \"gym_credit_transactions\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/member-credit-storage.ts\n"));

/***/ })

});