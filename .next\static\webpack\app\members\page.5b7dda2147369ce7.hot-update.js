"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/members/page",{

/***/ "(app-pages-browser)/./src/app/members/page.tsx":
/*!**********************************!*\
  !*** ./src/app/members/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MembersPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers_index__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/index */ \"(app-pages-browser)/./src/components/providers/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_export__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/export */ \"(app-pages-browser)/./src/lib/export.ts\");\n/* harmony import */ var _lib_test_data__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/test-data */ \"(app-pages-browser)/./src/lib/test-data.ts\");\n/* harmony import */ var _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/member-credit-storage */ \"(app-pages-browser)/./src/lib/member-credit-storage.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/members/add-member-modal */ \"(app-pages-browser)/./src/components/members/add-member-modal.tsx\");\n/* harmony import */ var _components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/members/edit-member-modal */ \"(app-pages-browser)/./src/components/members/edit-member-modal.tsx\");\n/* harmony import */ var _components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/members/view-member-modal */ \"(app-pages-browser)/./src/components/members/view-member-modal.tsx\");\n/* harmony import */ var _components_members_members_table__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/members/members-table */ \"(app-pages-browser)/./src/components/members/members-table.tsx\");\n/* harmony import */ var _components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/categories/categories-modal */ \"(app-pages-browser)/./src/components/categories/categories-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-spreadsheet.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tags.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,CheckSquare,Clock,Download,FileSpreadsheet,FileText,Plus,Search,Square,Tags,Trash2,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Remove database imports - using localStorage instead\n\n\n\n\n\n\n\n\nfunction MembersPage() {\n    _s();\n    const [members, setMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredMembers, setFilteredMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [creditFilter, setCreditFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedMembers, setSelectedMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [showAddModal, setShowAddModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showViewModal, setShowViewModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showCategoriesModal, setShowCategoriesModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingMember, setEditingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewingMember, setViewingMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [bulkLoading, setBulkLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t } = (0,_components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchMembers();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        filterMembers();\n    }, [\n        members,\n        searchQuery,\n        statusFilter,\n        creditFilter\n    ]);\n    const fetchMembers = ()=>{\n        try {\n            setLoading(true);\n            // Fetch users from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            // Fetch subscriptions from localStorage\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            // Map users with their subscriptions\n            const membersWithSubscriptions = users.map((user)=>({\n                    ...user,\n                    subscriptions: subscriptions.filter((sub)=>sub.user_id === user.id).map((sub)=>({\n                            id: sub.id,\n                            sport: sub.sport,\n                            plan_type: sub.plan_type,\n                            start_date: sub.start_date,\n                            end_date: sub.end_date,\n                            price_dzd: sub.price_dzd,\n                            status: sub.status\n                        }))\n                }));\n            // Sync credit data with members\n            _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_9__.MemberCreditStorage.syncMemberCreditData();\n            setMembers(membersWithSubscriptions);\n        } catch (error) {\n            console.error(\"Error loading members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load members from localStorage\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterMembers = ()=>{\n        // Add safety checks for member data\n        const validMembers = members.filter((member)=>member && typeof member === \"object\" && member.full_name && member.phone);\n        let filtered = validMembers.filter((member)=>{\n            var _member_full_name, _member_phone;\n            const nameMatch = ((_member_full_name = member.full_name) === null || _member_full_name === void 0 ? void 0 : _member_full_name.toLowerCase().includes(searchQuery.toLowerCase())) || false;\n            const phoneMatch = ((_member_phone = member.phone) === null || _member_phone === void 0 ? void 0 : _member_phone.includes(searchQuery)) || false;\n            const emailMatch = member.email ? member.email.toLowerCase().includes(searchQuery.toLowerCase()) : false;\n            return nameMatch || phoneMatch || emailMatch;\n        });\n        if (statusFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const subscriptions = member.subscriptions || [];\n                if (subscriptions.length === 0) {\n                    return statusFilter === \"expired\";\n                }\n                const activeSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date && (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(sub.end_date) === statusFilter);\n                return activeSubscriptions && activeSubscriptions.length > 0;\n            });\n        }\n        // Credit filtering\n        if (creditFilter !== \"all\") {\n            filtered = filtered.filter((member)=>{\n                const creditBalance = member.credit_balance || 0;\n                const creditLimit = member.credit_limit || 0;\n                switch(creditFilter){\n                    case \"has_credit\":\n                        return creditLimit > 0;\n                    case \"no_credit\":\n                        return creditLimit === 0;\n                    case \"over_limit\":\n                        return creditLimit > 0 && creditBalance > creditLimit * 0.8 // Over 80% of limit\n                        ;\n                    default:\n                        return true;\n                }\n            });\n        }\n        setFilteredMembers(filtered);\n    };\n    const getActiveSubscription = (member)=>{\n        if (!member) return null;\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return null;\n        // Get the most recent subscription (sorted by end date)\n        const sortedSubscriptions = subscriptions.filter((sub)=>sub && sub.end_date).sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        return sortedSubscriptions[0] || null;\n    };\n    const getMemberStatus = (member)=>{\n        if (!member) return \"expired\";\n        const subscriptions = member.subscriptions || [];\n        if (subscriptions.length === 0) return \"expired\";\n        // Get the most recent subscription (active or most recent)\n        const sortedSubscriptions = subscriptions.sort((a, b)=>new Date(b.end_date).getTime() - new Date(a.end_date).getTime());\n        const latestSubscription = sortedSubscriptions[0];\n        if (!latestSubscription || !latestSubscription.end_date) return \"expired\";\n        return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getSubscriptionStatus)(latestSubscription.end_date);\n    };\n    const deleteMember = (memberId)=>{\n        if (!confirm(\"Are you sure you want to delete this member?\")) return;\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>user.id !== memberId);\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>sub.user_id !== memberId);\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: t(\"member_deleted\"),\n                description: \"Member has been successfully deleted\"\n            });\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting member:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete member\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    // Export functions\n    const handleExportCSV = ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        const csvData = (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.convertToCSV)(exportData);\n        (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadCSV)(csvData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".csv\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to CSV successfully\"\n        });\n    };\n    const handleExportExcel = async ()=>{\n        const exportData = filteredMembers.map((member)=>({\n                ...member,\n                email: member.email || undefined,\n                pregnant: member.pregnant || undefined,\n                remarks: member.remarks || undefined,\n                subscriptions: member.subscriptions || []\n            }));\n        await (0,_lib_export__WEBPACK_IMPORTED_MODULE_7__.downloadExcel)(exportData, \"members-\".concat(new Date().toISOString().split(\"T\")[0], \".xlsx\"));\n        toast({\n            title: \"Export Complete\",\n            description: \"Members data exported to Excel successfully\"\n        });\n    };\n    // Bulk operations\n    const toggleSelectAll = ()=>{\n        if (selectedMembers.size === filteredMembers.length) {\n            setSelectedMembers(new Set());\n        } else {\n            setSelectedMembers(new Set(filteredMembers.map((m)=>m.id)));\n        }\n    };\n    const handleBulkDelete = ()=>{\n        if (selectedMembers.size === 0) return;\n        if (!confirm(\"Are you sure you want to delete \".concat(selectedMembers.size, \" members?\"))) return;\n        setBulkLoading(true);\n        try {\n            // Delete from localStorage\n            const storedUsers = localStorage.getItem(\"gym_members\");\n            const users = storedUsers ? JSON.parse(storedUsers) : [];\n            const updatedUsers = users.filter((user)=>!selectedMembers.has(user.id));\n            localStorage.setItem(\"gym_members\", JSON.stringify(updatedUsers));\n            // Also delete related subscriptions\n            const storedSubscriptions = localStorage.getItem(\"gym_subscriptions\");\n            const subscriptions = storedSubscriptions ? JSON.parse(storedSubscriptions) : [];\n            const updatedSubscriptions = subscriptions.filter((sub)=>!selectedMembers.has(sub.user_id));\n            localStorage.setItem(\"gym_subscriptions\", JSON.stringify(updatedSubscriptions));\n            toast({\n                title: \"Bulk Delete Complete\",\n                description: \"\".concat(selectedMembers.size, \" members deleted successfully\")\n            });\n            setSelectedMembers(new Set());\n            fetchMembers();\n        } catch (error) {\n            console.error(\"Error deleting members:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete selected members\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setBulkLoading(false);\n        }\n    };\n    const handleEditMember = (member)=>{\n        setEditingMember(member);\n        setShowEditModal(true);\n    };\n    const handleCreateTestData = ()=>{\n        (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.createTestMembers)();\n        toast({\n            title: \"Test Data Created\",\n            description: \"Test members with different subscription statuses have been created\"\n        });\n        fetchMembers();\n    };\n    const handleClearTestData = ()=>{\n        if (confirm(\"Are you sure you want to clear all data? This cannot be undone.\")) {\n            (0,_lib_test_data__WEBPACK_IMPORTED_MODULE_8__.clearTestData)();\n            toast({\n                title: \"Data Cleared\",\n                description: \"All test data has been removed\"\n            });\n            fetchMembers();\n        }\n    };\n    const handleViewDetails = (member)=>{\n        setViewingMember(member);\n        setShowViewModal(true);\n    };\n    const handlePrintReport = (member)=>{\n        // Create a simple print report\n        const printWindow = window.open(\"\", \"_blank\");\n        if (printWindow) {\n            const subscriptions = member.subscriptions || [];\n            printWindow.document.write(\"\\n        <html>\\n          <head>\\n            <title>Member Report - \".concat(member.full_name, '</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; }\\n              .header { text-align: center; margin-bottom: 30px; }\\n              .info { margin-bottom: 20px; }\\n              .label { font-weight: bold; }\\n              table { width: 100%; border-collapse: collapse; margin-top: 20px; }\\n              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\\n              th { background-color: #f2f2f2; }\\n            </style>\\n          </head>\\n          <body>\\n            <div class=\"header\">\\n              <h1>\\xc9LITE CLUB</h1>\\n              <h2>Member Report</h2>\\n            </div>\\n            <div class=\"info\">\\n              <p><span class=\"label\">Name:</span> ').concat(member.full_name, '</p>\\n              <p><span class=\"label\">Phone:</span> ').concat(member.phone, '</p>\\n              <p><span class=\"label\">Email:</span> ').concat(member.email || \"N/A\", '</p>\\n              <p><span class=\"label\">Age:</span> ').concat(member.age, '</p>\\n              <p><span class=\"label\">Gender:</span> ').concat(member.gender, '</p>\\n              <p><span class=\"label\">Status:</span> ').concat(member.situation, '</p>\\n              <p><span class=\"label\">Member Since:</span> ').concat(new Date(member.created_at).toLocaleDateString(), \"</p>\\n            </div>\\n            <h3>Subscriptions</h3>\\n            <table>\\n              <tr>\\n                <th>Sport</th>\\n                <th>Plan</th>\\n                <th>Start Date</th>\\n                <th>End Date</th>\\n                <th>Price</th>\\n                <th>Status</th>\\n              </tr>\\n              \").concat(subscriptions.map((sub)=>\"\\n                <tr>\\n                  <td>\".concat(sub.sport, \"</td>\\n                  <td>\").concat(sub.plan_type, \"</td>\\n                  <td>\").concat(sub.start_date, \"</td>\\n                  <td>\").concat(sub.end_date, \"</td>\\n                  <td>\").concat(sub.price_dzd, \" DZD</td>\\n                  <td>\").concat(sub.status, \"</td>\\n                </tr>\\n              \")).join(\"\"), '\\n            </table>\\n            <div style=\"margin-top: 40px; text-align: center; font-size: 12px;\">\\n              <p>Generated on ').concat(new Date().toLocaleString(), \"</p>\\n              <p>All rights reserved - Powered by iCode DZ Tel: +213 551 93 05 89</p>\\n            </div>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n            printWindow.print();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"members\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"members\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-black text-gray-900 dark:text-white tracking-tight\",\n                                    children: t(\"members_management\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-base font-semibold text-gray-600 dark:text-gray-400\",\n                                    children: t(\"manage_gym_members\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 449,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Export\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                    onClick: handleExportCSV,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_csv\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                    onClick: handleExportExcel,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"w-4 h-4 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        t(\"export_excel\")\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                selectedMembers.size > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuTrigger, {\n                                            asChild: true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"bulk_operations\"),\n                                                    \" (\",\n                                                    selectedMembers.size,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuContent, {\n                                            align: \"end\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_16__.DropdownMenuItem, {\n                                                onClick: handleBulkDelete,\n                                                className: \"text-red-600 dark:text-red-400\",\n                                                disabled: bulkLoading,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    t(\"delete_selected\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowCategoriesModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Categories\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this),\n                                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleCreateTestData,\n                                                    className: \"text-blue-600\",\n                                                    children: \"Create Test Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 506,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    onClick: handleClearTestData,\n                                                    className: \"text-red-600\",\n                                                    children: \"Clear Data\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            onClick: ()=>setShowAddModal(true),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"w-4 h-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"add_new_member\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600 dark:text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Total Members\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-5 h-5 text-green-600 dark:text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 542,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Active\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"active\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"w-5 h-5 text-orange-600 dark:text-orange-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expiring\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expiring\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-5 h-5 text-red-600 dark:text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-base font-bold text-gray-600 dark:text-gray-400\",\n                                                    children: \"Expired\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 578,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-black text-gray-900 dark:text-white\",\n                                                    children: members.filter((m)=>getMemberStatus(m) === \"expired\").length\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 577,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 572,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: toggleSelectAll,\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            selectedMembers.size === filteredMembers.length && filteredMembers.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm\",\n                                                children: selectedMembers.size > 0 ? \"\".concat(selectedMembers.size, \" \").concat(t(\"selected_count\")) : t(\"select_all\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 613,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t(\"search_members\"),\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        \"all\",\n                                        \"active\",\n                                        \"expiring\",\n                                        \"expired\"\n                                    ].map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: statusFilter === status ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setStatusFilter(status),\n                                            className: \"capitalize\",\n                                            children: t(status)\n                                        }, status, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        {\n                                            key: \"all\",\n                                            label: \"All Credit\"\n                                        },\n                                        {\n                                            key: \"has_credit\",\n                                            label: \"Has Credit\"\n                                        },\n                                        {\n                                            key: \"no_credit\",\n                                            label: \"No Credit\"\n                                        },\n                                        {\n                                            key: \"over_limit\",\n                                            label: \"High Usage\"\n                                        }\n                                    ].map((filter)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: creditFilter === filter.key ? \"gym\" : \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setCreditFilter(filter.key),\n                                            className: \"text-xs\",\n                                            children: filter.label\n                                        }, filter.key, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                            lineNumber: 646,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 590,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 589,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_members_table__WEBPACK_IMPORTED_MODULE_14__.MembersTable, {\n                            members: filteredMembers,\n                            loading: loading,\n                            selectedMembers: selectedMembers,\n                            onSelectionChange: setSelectedMembers,\n                            onEdit: handleEditMember,\n                            onDelete: deleteMember,\n                            onViewDetails: handleViewDetails,\n                            onPrintReport: handlePrintReport,\n                            onMemberUpdated: fetchMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 663,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 662,\n                    columnNumber: 9\n                }, this),\n                filteredMembers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"glass border-white/20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-2\",\n                                children: t(\"no_members_found\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 682,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-4\",\n                                children: searchQuery || statusFilter !== \"all\" ? t(\"try_adjusting_search\") : t(\"get_started_adding\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 685,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"gym\",\n                                onClick: ()=>setShowAddModal(true),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_CheckSquare_Clock_Download_FileSpreadsheet_FileText_Plus_Search_Square_Tags_Trash2_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 17\n                                    }, this),\n                                    t(\"add_new_member\")\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 679,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_add_member_modal__WEBPACK_IMPORTED_MODULE_11__.AddMemberModal, {\n                    open: showAddModal,\n                    onOpenChange: setShowAddModal,\n                    onMemberAdded: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 699,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_edit_member_modal__WEBPACK_IMPORTED_MODULE_12__.EditMemberModal, {\n                    open: showEditModal,\n                    onOpenChange: setShowEditModal,\n                    member: editingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 705,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_members_view_member_modal__WEBPACK_IMPORTED_MODULE_13__.ViewMemberModal, {\n                    open: showViewModal,\n                    onOpenChange: setShowViewModal,\n                    member: viewingMember,\n                    onMemberUpdated: fetchMembers\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_categories_categories_modal__WEBPACK_IMPORTED_MODULE_15__.CategoriesModal, {\n                    open: showCategoriesModal,\n                    onOpenChange: setShowCategoriesModal,\n                    onSportsUpdated: ()=>{\n                        // Sports updated - could refresh sports list if needed\n                        toast({\n                            title: \"Sports Updated\",\n                            description: \"Sports have been updated successfully\"\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n                    lineNumber: 719,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\members\\\\page.tsx\",\n        lineNumber: 441,\n        columnNumber: 5\n    }, this);\n}\n_s(MembersPage, \"4ahXRpUQDAkICKOZtdVCDHuX7k4=\", false, function() {\n    return [\n        _components_providers_index__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = MembersPage;\nvar _c;\n$RefreshReg$(_c, \"MembersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/members/page.tsx\n"));

/***/ })

});