"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/member-credit-storage */ \"(app-pages-browser)/./src/lib/member-credit-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Get product sales count for sorting\n    const getProductSalesCount = (productId)=>{\n        try {\n            const transactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            let salesCount = 0;\n            transactions.forEach((transaction)=>{\n                transaction.items.forEach((item)=>{\n                    var _products_find;\n                    if (item.name === ((_products_find = products.find((p)=>p.id === productId)) === null || _products_find === void 0 ? void 0 : _products_find.name)) {\n                        salesCount += item.quantity;\n                    }\n                });\n            });\n            return salesCount;\n        } catch (error) {\n            return 0;\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    }).sort((a, b)=>{\n        // Sort by sales count (popular products first), then by name\n        const aSales = getProductSalesCount(a.id);\n        const bSales = getProductSalesCount(b.id);\n        if (aSales !== bSales) {\n            return bSales - aSales // Higher sales first\n            ;\n        }\n        return a.name.localeCompare(b.name) // Alphabetical as secondary sort\n        ;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        // Validate credit limit for credit purchases\n        if (paymentType === \"credit\" && !validateCreditPurchase(member, getTotalAmount())) {\n            return; // Don't proceed if credit validation fails\n        }\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n        setShowPayment(true);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const validateCreditPurchase = (member, totalAmount)=>{\n        const creditCheck = _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.canMemberPurchaseOnCredit(member.id, totalAmount);\n        if (!creditCheck.canPurchase) {\n            if (creditCheck.creditLimit <= 0) {\n                toast({\n                    title: \"Credit Not Available\",\n                    description: \"\".concat(member.full_name, \" does not have a credit limit set.\"),\n                    variant: \"destructive\"\n                });\n            } else {\n                toast({\n                    title: \"Credit Limit Exceeded\",\n                    description: \"Purchase amount (\".concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(totalAmount), \") would exceed \").concat(member.full_name, \"'s available credit (\").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit), \").\"),\n                    variant: \"destructive\"\n                });\n            }\n            return false;\n        }\n        // Warning if approaching credit limit\n        if (creditCheck.availableCredit - totalAmount < creditCheck.creditLimit * 0.1) {\n            toast({\n                title: \"Credit Limit Warning\",\n                description: \"\".concat(member.full_name, \" will have only \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(creditCheck.availableCredit - totalAmount), \" credit remaining after this purchase.\"),\n                variant: \"default\"\n            });\n        }\n        return true;\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Handle credit transaction if payment type is credit\n            if (paymentType === \"credit\" && selectedMember && selectedMember.id !== \"guest\") {\n                const success = _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.updateMemberCreditBalance(selectedMember.id, selectedMember.full_name, getTotalAmount(), \"purchase\", transaction.id, \"POS Purchase - \".concat(cart.length, \" items\"));\n                if (!success) {\n                    throw new Error(\"Failed to process credit transaction\");\n                }\n                // Sync member credit data\n                _lib_member_credit_storage__WEBPACK_IMPORTED_MODULE_14__.MemberCreditStorage.syncMemberCreditData();\n            }\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 499,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 498,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 519,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 513,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 overflow-auto max-h-[calc(100vh-20rem)]\",\n                                        children: filteredProducts.map((product, index)=>{\n                                            const salesCount = getProductSalesCount(product.id);\n                                            const expired = isProductExpired(product);\n                                            const expiringSoon = isProductExpiringSoon(product);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                                className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer relative \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\", \" \").concat(salesCount > 0 && index < 3 ? \"ring-2 ring-yellow-400/50\" : \"\"),\n                                                onClick: ()=>!expired && addToCart(product),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                    className: \"p-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            salesCount > 0 && index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-bold shadow-lg z-10\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDD25 \",\n                                                                    salesCount\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 600,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center overflow-hidden\",\n                                                                children: [\n                                                                    product.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: product.image_url,\n                                                                        alt: product.name,\n                                                                        className: \"w-full h-full object-cover rounded-lg\",\n                                                                        onError: (e)=>{\n                                                                            var _target_nextElementSibling;\n                                                                            // Fallback to placeholder if image fails to load\n                                                                            const target = e.target;\n                                                                            target.style.display = \"none\";\n                                                                            (_target_nextElementSibling = target.nextElementSibling) === null || _target_nextElementSibling === void 0 ? void 0 : _target_nextElementSibling.classList.remove(\"hidden\");\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 606,\n                                                                        columnNumber: 27\n                                                                    }, this) : null,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-8 h-8 text-gray-400 \".concat(product.image_url ? \"hidden\" : \"\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-h-0 flex-1 overflow-hidden\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium text-xs text-gray-900 dark:text-white leading-tight mb-1 overflow-hidden text-ellipsis whitespace-nowrap\",\n                                                                        children: product.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 621,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1 truncate\",\n                                                                        children: product.category\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 624,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400 mb-1 truncate\",\n                                                                        children: [\n                                                                            \"Exp: \",\n                                                                            new Date(product.expiry_date).toLocaleDateString()\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs font-bold text-red-600 dark:text-red-400 truncate flex-1 mr-1\",\n                                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                                lineNumber: 633,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-gray-500 dark:text-gray-400 flex-shrink-0\",\n                                                                                children: product.stock\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                                lineNumber: 636,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 620,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, product.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 17\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 571,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 657,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 656,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 655,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 676,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 702,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 721,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 715,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 683,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 734,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 743,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 744,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 761,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 756,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 769,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 672,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 653,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 652,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 786,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 782,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 797,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 781,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 807,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 806,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 823,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 827,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 819,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 852,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 844,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 805,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 804,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 869,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 877,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 507,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});