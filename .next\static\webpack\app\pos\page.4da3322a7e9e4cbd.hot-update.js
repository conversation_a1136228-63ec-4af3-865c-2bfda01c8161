"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pos/page",{

/***/ "(app-pages-browser)/./src/app/pos/page.tsx":
/*!******************************!*\
  !*** ./src/app/pos/page.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ POSPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/main-layout */ \"(app-pages-browser)/./src/components/layout/main-layout.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_providers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers */ \"(app-pages-browser)/./src/components/providers.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/pos/scanner-modal */ \"(app-pages-browser)/./src/components/pos/scanner-modal.tsx\");\n/* harmony import */ var _components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pos/member-selection-modal */ \"(app-pages-browser)/./src/components/pos/member-selection-modal.tsx\");\n/* harmony import */ var _components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/pos/transaction-history */ \"(app-pages-browser)/./src/components/pos/transaction-history.tsx\");\n/* harmony import */ var _utils_demo_data__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/utils/demo-data */ \"(app-pages-browser)/./src/utils/demo-data.ts\");\n/* harmony import */ var _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/inventory-storage */ \"(app-pages-browser)/./src/lib/inventory-storage.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/qr-code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan-barcode.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/banknote.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/history.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Banknote,CreditCard,History,Minus,Package,Plus,QrCode,Receipt,ScanBarcode,Search,ShoppingCart,Trash2,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/receipt.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst categories = [\n    \"All\",\n    \"Supplements\",\n    \"Beverages\",\n    \"Snacks\",\n    \"Accessories\",\n    \"Equipment\"\n];\nfunction POSPage() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [cart, setCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"All\");\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showPayment, setShowPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [paymentType, setPaymentType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"cash\");\n    const [processing, setProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemberSelection, setShowMemberSelection] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTransactionHistory, setShowTransactionHistory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedMember, setSelectedMember] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scanType, setScanType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"both\");\n    const { t } = (0,_components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Initialize demo data on first load\n        (0,_utils_demo_data__WEBPACK_IMPORTED_MODULE_12__.initializeDemoData)();\n        fetchProducts();\n    }, []);\n    const fetchProducts = async ()=>{\n        try {\n            // Load from localStorage first (for development phase)\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            if (storedProducts.length > 0) {\n                const productData = storedProducts.filter((p)=>p.stock > 0);\n                setProducts(productData.map((p)=>({\n                        id: p.id,\n                        name: p.name,\n                        category: p.category,\n                        price_dzd: p.price_dzd,\n                        stock: p.stock,\n                        image_url: p.image_url,\n                        barcode: p.barcode,\n                        qr_code: p.qr_code,\n                        expiry_date: p.expiry_date\n                    })));\n                // Update categories based on loaded products\n                const uniqueCategories = [\n                    \"All\",\n                    ...new Set(productData.map((p)=>p.category))\n                ];\n                setCategories(uniqueCategories);\n            } else {\n                // Initialize with sample products if none exist\n                _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.initializeData();\n                // Retry loading after initialization\n                const initializedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n                if (initializedProducts.length > 0) {\n                    const productData = initializedProducts.filter((p)=>p.stock > 0);\n                    setProducts(productData.map((p)=>({\n                            id: p.id,\n                            name: p.name,\n                            category: p.category,\n                            price_dzd: p.price_dzd,\n                            stock: p.stock,\n                            image_url: p.image_url,\n                            barcode: p.barcode,\n                            qr_code: p.qr_code,\n                            expiry_date: p.expiry_date\n                        })));\n                    // Update categories based on loaded products\n                    const uniqueCategories = [\n                        \"All\",\n                        ...new Set(productData.map((p)=>p.category))\n                    ];\n                    setCategories(uniqueCategories);\n                }\n            }\n            // Optional: Try to sync with Supabase in the background (for future use)\n            try {\n                const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").select(\"*\").gt(\"stock\", 0).order(\"name\");\n                if (!error && data && data.length > 0) {\n                    // If Supabase has data, we could sync it here in the future\n                    console.log(\"Supabase products available for future sync:\", data.length);\n                }\n            } catch (supabaseError) {\n                // Supabase not available, continue with localStorage\n                console.log(\"Supabase not available, using localStorage\");\n            }\n        } catch (error) {\n            console.error(\"Error loading products:\", error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load products\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredProducts = products.filter((product)=>{\n        const matchesCategory = selectedCategory === \"All\" || product.category === selectedCategory;\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());\n        return matchesCategory && matchesSearch;\n    });\n    const addToCart = (product)=>{\n        setCart((prev)=>{\n            const existingItem = prev.find((item)=>item.id === product.id);\n            if (existingItem) {\n                if (existingItem.quantity < product.stock) {\n                    return prev.map((item)=>item.id === product.id ? {\n                            ...item,\n                            quantity: item.quantity + 1\n                        } : item);\n                } else {\n                    toast({\n                        title: \"Stock Limit\",\n                        description: \"Only \".concat(product.stock, \" items available\"),\n                        variant: \"destructive\"\n                    });\n                    return prev;\n                }\n            } else {\n                return [\n                    ...prev,\n                    {\n                        ...product,\n                        quantity: 1\n                    }\n                ];\n            }\n        });\n    };\n    const updateQuantity = (productId, newQuantity)=>{\n        if (newQuantity === 0) {\n            removeFromCart(productId);\n            return;\n        }\n        const product = products.find((p)=>p.id === productId);\n        if (product && newQuantity > product.stock) {\n            toast({\n                title: \"Stock Limit\",\n                description: \"Only \".concat(product.stock, \" items available\"),\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setCart((prev)=>prev.map((item)=>item.id === productId ? {\n                    ...item,\n                    quantity: newQuantity\n                } : item));\n    };\n    const removeFromCart = (productId)=>{\n        setCart((prev)=>prev.filter((item)=>item.id !== productId));\n    };\n    const clearCart = ()=>{\n        setCart([]);\n    };\n    const getTotalAmount = ()=>{\n        return cart.reduce((total, item)=>total + item.price_dzd * item.quantity, 0);\n    };\n    const getTotalItems = ()=>{\n        return cart.reduce((total, item)=>total + item.quantity, 0);\n    };\n    const handleScanSuccess = (result)=>{\n        // Try to find product by barcode or QR code\n        const product = products.find((p)=>p.barcode === result || p.qr_code === result || p.id === result);\n        if (product) {\n            addToCart(product);\n            toast({\n                title: t(\"scan_result\"),\n                description: \"\".concat(product.name, \" added to cart\")\n            });\n        } else {\n            toast({\n                title: t(\"invalid_code\"),\n                description: t(\"code_not_found\"),\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleMemberSelection = (member)=>{\n        setSelectedMember(member);\n        setShowMemberSelection(false);\n    };\n    const handlePaymentTypeSelection = (type)=>{\n        setPaymentType(type);\n        if (type === \"credit\") {\n            setShowMemberSelection(true);\n        } else {\n            setSelectedMember(null);\n            setShowPayment(true);\n        }\n    };\n    const isProductExpired = (product)=>{\n        if (!product.expiry_date) return false;\n        return new Date(product.expiry_date) < new Date();\n    };\n    const isProductExpiringSoon = (product)=>{\n        if (!product.expiry_date) return false;\n        const expiryDate = new Date(product.expiry_date);\n        const today = new Date();\n        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n        return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n    };\n    const processSale = async ()=>{\n        if (cart.length === 0) return;\n        setProcessing(true);\n        try {\n            // Create transaction record\n            const transaction = {\n                id: \"TXN-\".concat(Date.now()),\n                date: new Date().toISOString().split(\"T\")[0],\n                customer_name: selectedMember ? selectedMember.full_name : t(\"guest_customer\"),\n                customer_type: selectedMember && selectedMember.id !== \"guest\" ? \"member\" : \"guest\",\n                payment_type: paymentType,\n                items: cart.map((item)=>({\n                        name: item.name,\n                        quantity: item.quantity,\n                        price: item.price_dzd\n                    })),\n                total_amount: getTotalAmount(),\n                created_at: new Date().toISOString()\n            };\n            // Save transaction to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem(\"gym_transactions\") || \"[]\");\n            existingTransactions.push(transaction);\n            localStorage.setItem(\"gym_transactions\", JSON.stringify(existingTransactions));\n            // Update product stock in localStorage using InventoryStorage\n            const storedProducts = _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.getFromStorage(\"gym_products\");\n            const updatedProducts = storedProducts.map((product)=>{\n                const cartItem = cart.find((item)=>item.id === product.id);\n                if (cartItem) {\n                    // Create stock movement record\n                    _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.addStockMovement({\n                        product_id: product.id,\n                        product_name: product.name,\n                        movement_type: \"sale\",\n                        quantity_change: -cartItem.quantity,\n                        previous_stock: product.stock,\n                        new_stock: product.stock - cartItem.quantity,\n                        reference_id: transaction.id,\n                        reference_type: \"sale\",\n                        notes: \"Sale transaction - \".concat(cartItem.quantity, \" units sold\")\n                    });\n                    return {\n                        ...product,\n                        stock: product.stock - cartItem.quantity,\n                        updated_at: new Date().toISOString()\n                    };\n                }\n                return product;\n            });\n            _lib_inventory_storage__WEBPACK_IMPORTED_MODULE_13__.InventoryStorage.saveToStorage(\"gym_products\", updatedProducts);\n            // Try to sync with Supabase if available\n            try {\n                const { data: sale, error: saleError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales\").insert({\n                    total_price_dzd: getTotalAmount(),\n                    payment_type: paymentType,\n                    customer_name: transaction.customer_name,\n                    customer_type: transaction.customer_type\n                }).select().single();\n                if (!saleError && sale) {\n                    // Create sale items\n                    const saleItems = cart.map((item)=>({\n                            sale_id: sale.id,\n                            product_id: item.id,\n                            quantity: item.quantity,\n                            unit_price_dzd: item.price_dzd\n                        }));\n                    await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"sales_items\").insert(saleItems);\n                    // Update product stock in Supabase\n                    for (const item of cart){\n                        await _lib_supabase__WEBPACK_IMPORTED_MODULE_7__.supabase.from(\"products\").update({\n                            stock: item.stock - item.quantity\n                        }).eq(\"id\", item.id);\n                    }\n                }\n            } catch (supabaseError) {\n                console.log(\"Supabase sync failed, continuing with local storage\");\n            }\n            toast({\n                title: t(\"sale_completed\"),\n                description: \"\".concat(t(\"sale_completed\"), \": \").concat((0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount()))\n            });\n            // Reset state\n            clearCart();\n            setShowPayment(false);\n            setSelectedMember(null);\n            fetchProducts() // Refresh products to update stock\n            ;\n        } catch (error) {\n            toast({\n                title: t(\"sale_failed\"),\n                description: t(\"sale_failed\"),\n                variant: \"destructive\"\n            });\n        } finally{\n            setProcessing(false);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n            title: t(\"pos\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 407,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n            lineNumber: 406,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_main_layout__WEBPACK_IMPORTED_MODULE_2__.MainLayout, {\n        title: t(\"pos\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"glass border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t(\"search_products\"),\n                                                                value: searchQuery,\n                                                                onChange: (e)=>setSearchQuery(e.target.value),\n                                                                className: \"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 427,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"qr\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_qr_code\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 435,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>{\n                                                            setScanType(\"barcode\");\n                                                            setShowScanner(true);\n                                                        },\n                                                        title: t(\"scan_barcode\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-4 h-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: selectedCategory === category ? \"gym\" : \"outline\",\n                                                        size: \"sm\",\n                                                        onClick: ()=>setSelectedCategory(category),\n                                                        className: \"text-xs\",\n                                                        children: category\n                                                    }, category, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 421,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 overflow-auto max-h-[calc(100vh-16rem)]\",\n                                children: filteredProducts.map((product)=>{\n                                    const expired = isProductExpired(product);\n                                    const expiringSoon = isProductExpiringSoon(product);\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        className: \"glass border-white/20 hover:shadow-lg transition-all duration-200 cursor-pointer \".concat(expired ? \"opacity-50 border-red-500\" : expiringSoon ? \"border-yellow-500\" : \"\"),\n                                        onClick: ()=>!expired && addToCart(product),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    (expired || expiringSoon) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1 text-xs \".concat(expired ? \"text-red-500\" : \"text-yellow-500\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: expired ? t(\"product_expired\") : t(\"expires_soon\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full h-20 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                            lineNumber: 504,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm text-gray-900 dark:text-white line-clamp-2\",\n                                                                children: product.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 507,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: product.category\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            product.expiry_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                children: [\n                                                                    t(\"expiry_date\"),\n                                                                    \": \",\n                                                                    new Date(product.expiry_date).toLocaleDateString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-bold text-red-600 dark:text-red-400\",\n                                                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(product.price_dzd)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 519,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            t(\"stock\"),\n                                                                            \": \",\n                                                                            product.stock\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 522,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 518,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 491,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"glass border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    className: \"pb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Cart (\",\n                                                            getTotalItems(),\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 17\n                                            }, this),\n                                            cart.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: clearCart,\n                                                className: \"text-red-500 hover:text-red-700\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 545,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 539,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: cart.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-gray-500 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-12 h-12 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Cart is empty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 max-h-64 overflow-auto\",\n                                                children: cart.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"font-medium text-sm text-gray-900 dark:text-white\",\n                                                                        children: item.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                                        children: [\n                                                                            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(item.price_dzd),\n                                                                            \" each\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity - 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 580,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"w-8 text-center text-sm font-medium\",\n                                                                        children: item.quantity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 588,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6\",\n                                                                        onClick: ()=>updateQuantity(item.id, item.quantity + 1),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 597,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 591,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-6 w-6 text-red-500 hover:text-red-700\",\n                                                                        onClick: ()=>removeFromCart(item.id),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"w-3 h-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                            lineNumber: 605,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 599,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-t border-gray-200 dark:border-gray-700 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-center mb-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                                children: \"Total:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 615,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl font-bold text-red-600 dark:text-red-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 614,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 627,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-blue-700 dark:text-blue-300\",\n                                                                        children: selectedMember.full_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 628,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 626,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-600 dark:text-blue-400 mt-1\",\n                                                                children: selectedMember.id === \"guest\" ? t(\"guest_customer\") : t(\"select_member\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"cash\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_cash\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"gym-secondary\",\n                                                                className: \"w-full\",\n                                                                onClick: ()=>handlePaymentTypeSelection(\"credit\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"w-4 h-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                        lineNumber: 653,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    t(\"pay_credit\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 639,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: t(\"transaction_history\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setShowTransactionHistory(!showTransactionHistory),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 675,\n                                                columnNumber: 15\n                                            }, this),\n                                            showTransactionHistory ? \"Hide\" : \"Show\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 670,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this),\n                            showTransactionHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_transaction_history__WEBPACK_IMPORTED_MODULE_11__.TransactionHistory, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                        lineNumber: 665,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            showPayment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Confirm Payment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 692,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: ()=>setShowPayment(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-gray-900 dark:text-white mb-2\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(getTotalAmount())\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400\",\n                                            children: [\n                                                t(\"payment_method\"),\n                                                \": \",\n                                                paymentType === \"cash\" ? t(\"cash\") : t(\"credit\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedMember && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 dark:text-gray-300\",\n                                                children: [\n                                                    t(\"customer\"),\n                                                    \": \",\n                                                    selectedMember.full_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"outline\",\n                                            className: \"flex-1\",\n                                            onClick: ()=>setShowPayment(false),\n                                            disabled: processing,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"gym\",\n                                            className: \"flex-1\",\n                                            onClick: processSale,\n                                            disabled: processing,\n                                            children: processing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"processing\")\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Banknote_CreditCard_History_Minus_Package_Plus_QrCode_Receipt_ScanBarcode_Search_ShoppingCart_Trash2_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    t(\"confirm_sale\")\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                    lineNumber: 689,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 688,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_scanner_modal__WEBPACK_IMPORTED_MODULE_9__.ScannerModal, {\n                isOpen: showScanner,\n                onClose: ()=>setShowScanner(false),\n                onScanSuccess: handleScanSuccess,\n                scanType: scanType\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pos_member_selection_modal__WEBPACK_IMPORTED_MODULE_10__.MemberSelectionModal, {\n                isOpen: showMemberSelection,\n                onClose: ()=>setShowMemberSelection(false),\n                onSelectMember: (member)=>{\n                    handleMemberSelection(member);\n                    setShowPayment(true);\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n                lineNumber: 761,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\POS GYM ELITE\\\\src\\\\app\\\\pos\\\\page.tsx\",\n        lineNumber: 415,\n        columnNumber: 5\n    }, this);\n}\n_s(POSPage, \"7MnZZ0TOiKp7QClTVXYR0pULVAg=\", false, function() {\n    return [\n        _components_providers__WEBPACK_IMPORTED_MODULE_5__.useLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = POSPage;\nvar _c;\n$RefreshReg$(_c, \"POSPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/pos/page.tsx\n"));

/***/ })

});